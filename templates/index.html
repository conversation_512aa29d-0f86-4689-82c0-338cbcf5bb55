<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能浏览器搜索工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 25px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .section h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .input-group {
            display: flex;
            gap: 15px;
            align-items: end;
        }
        
        .input-group .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .progress-container {
            margin: 20px 0;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            height: 8px;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .result-area {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            min-height: 300px;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
            white-space: pre-wrap;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 25px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
            font-weight: 600;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .status-ready { background: #28a745; }
        .status-searching { background: #007bff; }
        .status-error { background: #dc3545; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 智能浏览器搜索工具</h1>
            <p>基于AI的智能网络搜索助手</p>
        </div>
        
        <div class="content">
            <!-- API配置区域 -->
            <div class="section">
                <h3>🔧 API 配置</h3>
                <div class="input-group">
                    <div class="form-group">
                        <label for="apiKey">API Key:</label>
                        <input type="password" id="apiKey" placeholder="请输入您的API Key">
                    </div>
                    <div class="form-group">
                        <label for="baseUrl">Base URL:</label>
                        <input type="text" id="baseUrl" value="https://api.deepseek.com/v1" placeholder="API基础URL">
                    </div>
                    <button class="btn btn-primary" onclick="initializeAI()">初始化AI</button>
                </div>
            </div>
            
            <!-- 搜索区域 -->
            <div class="section">
                <h3>🔍 搜索</h3>
                <div class="input-group">
                    <div class="form-group">
                        <label for="searchQuery">搜索内容:</label>
                        <input type="text" id="searchQuery" value="iPhone 16 Pro Max 价格 多少钱" placeholder="请输入搜索关键词">
                    </div>
                    <button class="btn btn-primary" id="searchBtn" onclick="startSearch()" disabled>开始搜索</button>
                    <button class="btn btn-danger" id="stopBtn" onclick="stopSearch()" disabled>停止搜索</button>
                </div>
                
                <div class="progress-container">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
            </div>
            
            <!-- 结果显示区域 -->
            <div class="section">
                <h3>📋 搜索结果</h3>
                <div class="result-area" id="resultArea">等待搜索结果...</div>
                
                <div class="button-group">
                    <button class="btn btn-secondary" onclick="clearResults()">清空结果</button>
                    <button class="btn btn-secondary" onclick="saveResults()">保存结果</button>
                </div>
            </div>
        </div>
        
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot status-ready" id="statusDot"></div>
                <span id="statusText">准备就绪</span>
            </div>
            <div>
                <span id="aiStatus">AI未初始化</span>
            </div>
        </div>
    </div>

    <script>
        let searchInterval;
        let isAIInitialized = false;
        
        // 初始化AI
        function initializeAI() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const baseUrl = document.getElementById('baseUrl').value.trim();
            
            if (!apiKey || !baseUrl) {
                showAlert('请填写完整的API配置信息', 'error');
                return;
            }
            
            updateStatus('正在初始化AI...', 'searching');
            
            fetch('/initialize', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    api_key: apiKey,
                    base_url: baseUrl
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    isAIInitialized = true;
                    document.getElementById('searchBtn').disabled = false;
                    document.getElementById('aiStatus').textContent = 'AI已初始化';
                    updateStatus('AI初始化成功', 'ready');
                    showAlert(data.message, 'success');
                } else {
                    updateStatus('AI初始化失败', 'error');
                    showAlert(data.message, 'error');
                }
            })
            .catch(error => {
                updateStatus('AI初始化失败', 'error');
                showAlert('网络错误: ' + error.message, 'error');
            });
        }
        
        // 开始搜索
        function startSearch() {
            const query = document.getElementById('searchQuery').value.trim();
            
            if (!query) {
                showAlert('请输入搜索内容', 'error');
                return;
            }
            
            document.getElementById('searchBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('resultArea').textContent = '正在搜索...';
            updateStatus('正在搜索...', 'searching');
            
            fetch('/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: query
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 开始轮询搜索状态
                    searchInterval = setInterval(checkSearchStatus, 1000);
                } else {
                    document.getElementById('searchBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                    updateStatus('搜索失败', 'error');
                    showAlert(data.message, 'error');
                }
            })
            .catch(error => {
                document.getElementById('searchBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                updateStatus('搜索失败', 'error');
                showAlert('网络错误: ' + error.message, 'error');
            });
        }
        
        // 检查搜索状态
        function checkSearchStatus() {
            fetch('/status')
            .then(response => response.json())
            .then(data => {
                // 更新进度条
                document.getElementById('progressBar').style.width = data.progress + '%';
                
                if (!data.is_searching) {
                    // 搜索完成
                    clearInterval(searchInterval);
                    document.getElementById('searchBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                    document.getElementById('progressBar').style.width = '0%';
                    
                    if (data.result) {
                        document.getElementById('resultArea').textContent = data.result;
                        updateStatus('搜索完成', 'ready');
                    } else if (data.error) {
                        document.getElementById('resultArea').textContent = data.error;
                        updateStatus('搜索失败', 'error');
                        showAlert(data.error, 'error');
                    }
                }
            })
            .catch(error => {
                clearInterval(searchInterval);
                document.getElementById('searchBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                updateStatus('状态检查失败', 'error');
            });
        }
        
        // 停止搜索
        function stopSearch() {
            fetch('/stop', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(searchInterval);
                document.getElementById('searchBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                document.getElementById('progressBar').style.width = '0%';
                updateStatus('搜索已停止', 'ready');
                showAlert(data.message, 'success');
            });
        }
        
        // 清空结果
        function clearResults() {
            document.getElementById('resultArea').textContent = '等待搜索结果...';
            updateStatus('结果已清空', 'ready');
        }
        
        // 保存结果
        function saveResults() {
            const content = document.getElementById('resultArea').textContent;
            if (!content || content === '等待搜索结果...' || content === '正在搜索...') {
                showAlert('没有结果可保存', 'error');
                return;
            }
            
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'search_result_' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            showAlert('结果已保存', 'success');
        }
        
        // 更新状态
        function updateStatus(text, type) {
            document.getElementById('statusText').textContent = text;
            const statusDot = document.getElementById('statusDot');
            statusDot.className = 'status-dot status-' + type;
        }
        
        // 显示提示信息
        function showAlert(message, type) {
            // 移除现有的提示
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());
            
            // 创建新提示
            const alert = document.createElement('div');
            alert.className = 'alert alert-' + type;
            alert.textContent = message;
            
            // 插入到第一个section之前
            const firstSection = document.querySelector('.section');
            firstSection.parentNode.insertBefore(alert, firstSection);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }
        
        // 回车键搜索
        document.getElementById('searchQuery').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !document.getElementById('searchBtn').disabled) {
                startSearch();
            }
        });
        
        // 页面加载时检查状态
        window.onload = function() {
            fetch('/status')
            .then(response => response.json())
            .then(data => {
                if (data.has_agent) {
                    isAIInitialized = true;
                    document.getElementById('searchBtn').disabled = false;
                    document.getElementById('aiStatus').textContent = 'AI已初始化';
                }
            });
        };
    </script>
</body>
</html>