import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from langchain_openai import ChatOpenAI
from langchain.agents import initialize_agent, AgentType
from langchain.tools import BaseTool
from browser_use import BrowserUse
from pydantic import SecretStr, BaseModel, Field
from typing import Optional, Type

class SearchTool(BaseTool):
    name = "web_search"
    description = "用于在网络上搜索信息的工具，输入搜索关键词"
    
    def _run(self, query: str) -> str:
        browser = BrowserUse(headless=False)
        result = browser.search(query)
        browser.quit()
        return result
    
    async def _arun(self, query: str) -> str:
        return self._run(query)

class BrowserGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("智能浏览器搜索工具")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        self.llm = None
        self.agent = None
        self.search_running = False
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="API 配置", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(config_frame, text="API Key:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.api_key_entry = ttk.Entry(config_frame, width=50, show="*")
        self.api_key_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.api_key_entry.insert(0, "api_key")
        
        ttk.Label(config_frame, text="Base URL:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.base_url_entry = ttk.Entry(config_frame, width=50)
        self.base_url_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(5, 0))
        self.base_url_entry.insert(0, "https://api.deepseek.com/v1")
        
        self.init_button = ttk.Button(config_frame, text="初始化AI", command=self.initialize_ai)
        self.init_button.grid(row=0, column=2, rowspan=2, padx=(10, 0))
        
        # 搜索区域
        search_frame = ttk.LabelFrame(main_frame, text="搜索", padding="10")
        search_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(search_frame, text="搜索内容:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.search_entry = ttk.Entry(search_frame, width=60)
        self.search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.search_entry.insert(0, "iPhone 16 Pro Max 价格 多少钱")
        self.search_entry.bind('<Return>', lambda e: self.start_search())
        
        self.search_button = ttk.Button(search_frame, text="开始搜索", command=self.start_search)
        self.search_button.grid(row=0, column=2)
        
        self.stop_button = ttk.Button(search_frame, text="停止搜索", command=self.stop_search, state='disabled')
        self.stop_button.grid(row=0, column=3, padx=(5, 0))
        
        # 进度条
        self.progress = ttk.Progressbar(search_frame, mode='indeterminate')
        self.progress.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="搜索结果", padding="10")
        result_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.result_text = scrolledtext.ScrolledText(result_frame, width=80, height=20, wrap=tk.WORD)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        self.clear_button = ttk.Button(button_frame, text="清空结果", command=self.clear_results)
        self.clear_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.save_button = ttk.Button(button_frame, text="保存结果", command=self.save_results)
        self.save_button.pack(side=tk.LEFT)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        config_frame.columnconfigure(1, weight=1)
        search_frame.columnconfigure(1, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
    def initialize_ai(self):
        try:
            api_key = self.api_key_entry.get().strip()
            base_url = self.base_url_entry.get().strip()
            
            if not api_key or not base_url:
                messagebox.showerror("错误", "请填写完整的API配置信息")
                return
            
            self.status_var.set("正在初始化AI...")
            self.init_button.config(state='disabled')
            
            self.llm = ChatOpenAI(
                base_url=base_url,
                model="deepseek-reasoner",
                api_key=SecretStr(api_key),
                temperature=0
            )
            
            tools = [SearchTool()]
            self.agent = initialize_agent(
                tools=tools,
                llm=self.llm,
                agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
                verbose=True
            )
            
            self.status_var.set("AI初始化成功")
            messagebox.showinfo("成功", "AI初始化完成！")
            
        except Exception as e:
            self.status_var.set("AI初始化失败")
            messagebox.showerror("错误", f"AI初始化失败: {str(e)}")
        finally:
            self.init_button.config(state='normal')
    
    def start_search(self):
        if not self.agent:
            messagebox.showwarning("警告", "请先初始化AI")
            return
        
        query = self.search_entry.get().strip()
        if not query:
            messagebox.showwarning("警告", "请输入搜索内容")
            return
        
        if self.search_running:
            return
        
        self.search_running = True
        self.search_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.progress.start()
        self.status_var.set("正在搜索...")
        
        # 在单独线程中执行搜索
        search_thread = threading.Thread(target=self.perform_search, args=(query,))
        search_thread.daemon = True
        search_thread.start()
    
    def perform_search(self, query):
        try:
            prompt = f"请搜索并告诉我{query}的详细信息，包括不同配置的价格。"
            result = self.agent.run(prompt)
            
            # 在主线程中更新UI
            self.root.after(0, self.update_search_result, result)
            
        except Exception as e:
            error_msg = f"搜索过程中出现错误: {str(e)}"
            self.root.after(0, self.update_search_error, error_msg)
    
    def update_search_result(self, result):
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, result)
        self.finish_search("搜索完成")
    
    def update_search_error(self, error_msg):
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, error_msg)
        self.finish_search("搜索失败")
        messagebox.showerror("搜索错误", error_msg)
    
    def finish_search(self, status_msg):
        self.search_running = False
        self.search_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress.stop()
        self.status_var.set(status_msg)
    
    def stop_search(self):
        self.search_running = False
        self.finish_search("搜索已停止")
    
    def clear_results(self):
        self.result_text.delete(1.0, tk.END)
        self.status_var.set("结果已清空")
    
    def save_results(self):
        try:
            from tkinter import filedialog
            content = self.result_text.get(1.0, tk.END)
            if not content.strip():
                messagebox.showwarning("警告", "没有结果可保存")
                return
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("成功", f"结果已保存到: {filename}")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")

def main():
    root = tk.Tk()
    app = BrowserGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()