from flask import Flask, request, jsonify
from auth_middleware import <PERSON><PERSON><PERSON><PERSON>, Role, require_auth, AuthenticationError, AuthorizationError, RateLimitExceededError
import secrets
from datetime import datetime
from typing import Dict, List

app = Flask(__name__)

SECRET_KEY = secrets.token_urlsafe(32)
auth_system = ModelAPIAuth(SECRET_KEY)

def create_default_admin():
    admin_id = auth_system.create_user(
        username="admin",
        password="admin123",
        roles=[Role.ADMIN],
        api_quota=10000
    )
    return admin_id

admin_user_id = create_default_admin()

@app.route('/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({'error': 'Username and password required'}), 400
        
        user_id = auth_system.authenticate_user(username, password)
        if not user_id:
            return jsonify({'error': 'Invalid credentials'}), 401
        
        token = auth_system.generate_token(user_id)
        user = auth_system.users[user_id]
        
        return jsonify({
            'token': token,
            'user': {
                'id': user_id,
                'username': user.username,
                'roles': [role.value for role in user.roles]
            }
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/auth/users', methods=['POST'])
@require_auth(required_roles=[Role.ADMIN])
def create_user():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        roles_str = data.get('roles', ['user'])
        api_quota = data.get('api_quota', 1000)
        
        if not username or not password:
            return jsonify({'error': 'Username and password required'}), 400
        
        roles = []
        for role_str in roles_str:
            try:
                roles.append(Role(role_str))
            except ValueError:
                return jsonify({'error': f'Invalid role: {role_str}'}), 400
        
        for existing_user in auth_system.users.values():
            if existing_user.username == username:
                return jsonify({'error': 'Username already exists'}), 409
        
        user_id = auth_system.create_user(username, password, roles, api_quota)
        
        return jsonify({
            'user_id': user_id,
            'username': username,
            'roles': [role.value for role in roles],
            'api_quota': api_quota
        }), 201
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/auth/users', methods=['GET'])
@require_auth(required_roles=[Role.ADMIN])
def list_users():
    try:
        users_list = []
        for user_id, user in auth_system.users.items():
            users_list.append({
                'user_id': user_id,
                'username': user.username,
                'roles': [role.value for role in user.roles],
                'api_quota': user.api_quota,
                'is_active': user.is_active,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'last_login': user.last_login.isoformat() if user.last_login else None
            })
        
        return jsonify({'users': users_list})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/auth/users/<user_id>', methods=['PUT'])
@require_auth(required_roles=[Role.ADMIN])
def update_user(user_id):
    try:
        if user_id not in auth_system.users:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json()
        user = auth_system.users[user_id]
        
        if 'roles' in data:
            roles = []
            for role_str in data['roles']:
                try:
                    roles.append(Role(role_str))
                except ValueError:
                    return jsonify({'error': f'Invalid role: {role_str}'}), 400
            user.roles = roles
        
        if 'api_quota' in data:
            user.api_quota = data['api_quota']
        
        if 'is_active' in data:
            user.is_active = data['is_active']
        
        return jsonify({
            'user_id': user_id,
            'username': user.username,
            'roles': [role.value for role in user.roles],
            'api_quota': user.api_quota,
            'is_active': user.is_active
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/auth/users/<user_id>', methods=['DELETE'])
@require_auth(required_roles=[Role.ADMIN])
def delete_user(user_id):
    try:
        if user_id not in auth_system.users:
            return jsonify({'error': 'User not found'}), 404
        
        user = auth_system.users[user_id]
        if Role.ADMIN in user.roles and len([u for u in auth_system.users.values() if Role.ADMIN in u.roles]) == 1:
            return jsonify({'error': 'Cannot delete the last admin user'}), 400
        
        del auth_system.users[user_id]
        if user_id in auth_system.user_sessions:
            del auth_system.user_sessions[user_id]
        if user_id in auth_system.rate_limits:
            del auth_system.rate_limits[user_id]
        
        return jsonify({'message': 'User deleted successfully'})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/auth/profile', methods=['GET'])
@require_auth()
def get_profile():
    try:
        current_user = request.current_user
        user_id = current_user['user_id']
        user = auth_system.users[user_id]
        
        return jsonify({
            'user_id': user_id,
            'username': user.username,
            'roles': [role.value for role in user.roles],
            'api_quota': user.api_quota,
            'is_active': user.is_active,
            'created_at': user.created_at.isoformat() if user.created_at else None,
            'last_login': user.last_login.isoformat() if user.last_login else None
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/models/<model_name>/inference', methods=['POST'])
@require_auth(required_roles=[Role.USER, Role.ADMIN])
def model_inference(model_name):
    try:
        current_user = request.current_user
        data = request.get_json()
        
        allowed_models = ['gpt-3.5', 'claude', 'llama2']
        if model_name not in allowed_models:
            return jsonify({'error': 'Model not available'}), 404
        
        return jsonify({
            'model': model_name,
            'user': current_user['username'],
            'response': f"Mock response from {model_name}",
            'timestamp': datetime.utcnow().isoformat()
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.errorhandler(AuthenticationError)
def handle_auth_error(e):
    return jsonify({'error': str(e)}), 401

@app.errorhandler(AuthorizationError)
def handle_authz_error(e):
    return jsonify({'error': str(e)}), 403

@app.errorhandler(RateLimitExceededError)
def handle_rate_limit_error(e):
    return jsonify({'error': str(e)}), 429

@app.before_request
def inject_auth_system():
    request.auth_system = auth_system

if __name__ == '__main__':
    print(f"Default admin credentials - Username: admin, Password: admin123")
    print(f"Admin user ID: {admin_user_id}")
    app.run(debug=True, host='0.0.0.0', port=5000)