{"models": [{"id": "gpt-4o", "name": "GPT-4o", "description": "OpenAI最新的多模态模型，支持文本和图像输入，具有强大的推理能力", "provider": "OpenAI", "series": "gpt", "categories": ["general", "reasoning", "programming"], "input_modalities": ["text", "image"], "pricing": {"prompt_price": 0.035, "completion_price": 0.105, "image_price": null, "currency": "CNY"}, "capabilities": {"context_length": 128000, "max_output_tokens": 4096, "supports_tools": true, "supports_vision": true, "supports_streaming": true, "supports_json_mode": true, "supports_function_calling": true, "temperature_range": [0.0, 2.0], "top_p_range": [0.0, 1.0]}, "status": "available", "created_at": "2025-06-25T12:42:55.811539", "updated_at": "2025-06-25T12:42:55.811540", "tags": ["最新", "多模态", "推理"], "homepage": "https://openai.com/gpt-4", "documentation": "https://platform.openai.com/docs/models/gpt-4"}, {"id": "gpt-4-turbo", "name": "GPT-4 Turbo", "description": "GPT-4的优化版本，更快的响应速度和更低的成本", "provider": "OpenAI", "series": "gpt", "categories": ["general", "programming", "creative"], "input_modalities": ["text", "image"], "pricing": {"prompt_price": 0.07, "completion_price": 0.21, "image_price": null, "currency": "CNY"}, "capabilities": {"context_length": 128000, "max_output_tokens": 4096, "supports_tools": true, "supports_vision": true, "supports_streaming": true, "supports_json_mode": true, "supports_function_calling": true, "temperature_range": [0.0, 2.0], "top_p_range": [0.0, 1.0]}, "status": "available", "created_at": "2025-06-25T12:42:55.812269", "updated_at": "2025-06-25T12:42:55.812271", "tags": ["快速", "多模态"], "homepage": "https://openai.com/gpt-4", "documentation": "https://platform.openai.com/docs/models/gpt-4"}, {"id": "gpt-3.5-turbo", "name": "GPT-3.5 Turbo", "description": "经济实惠的对话模型，适合大多数应用场景", "provider": "OpenAI", "series": "gpt", "categories": ["general", "creative"], "input_modalities": ["text"], "pricing": {"prompt_price": 0.0035, "completion_price": 0.0105, "image_price": null, "currency": "CNY"}, "capabilities": {"context_length": 16385, "max_output_tokens": 4096, "supports_tools": true, "supports_vision": false, "supports_streaming": true, "supports_json_mode": true, "supports_function_calling": true, "temperature_range": [0.0, 2.0], "top_p_range": [0.0, 1.0]}, "status": "available", "created_at": "2025-06-25T12:42:55.812964", "updated_at": "2025-06-25T12:42:55.812965", "tags": ["经济", "对话"], "homepage": "https://openai.com/gpt-3-5", "documentation": "https://platform.openai.com/docs/models/gpt-3-5"}, {"id": "claude-3-5-sonnet", "name": "Claude 3.5 Sonnet", "description": "Anthropic最强大的模型，在推理、数学和编程方面表现卓越", "provider": "Anthropic", "series": "claude", "categories": ["reasoning", "programming", "analysis"], "input_modalities": ["text", "image"], "pricing": {"prompt_price": 0.21, "completion_price": 1.05, "image_price": null, "currency": "CNY"}, "capabilities": {"context_length": 200000, "max_output_tokens": 8192, "supports_tools": false, "supports_vision": true, "supports_streaming": true, "supports_json_mode": true, "supports_function_calling": false, "temperature_range": [0.0, 2.0], "top_p_range": [0.0, 1.0]}, "status": "available", "created_at": "2025-06-25T12:42:55.813766", "updated_at": "2025-06-25T12:42:55.813767", "tags": ["推理", "编程", "分析"], "homepage": "https://www.anthropic.com/claude", "documentation": "https://docs.anthropic.com/claude/docs"}, {"id": "claude-3-haiku", "name": "Claude 3 Haiku", "description": "快速且经济的模型，适合简单任务和高频调用", "provider": "Anthropic", "series": "claude", "categories": ["general"], "input_modalities": ["text", "image"], "pricing": {"prompt_price": 0.0175, "completion_price": 0.0875, "image_price": null, "currency": "CNY"}, "capabilities": {"context_length": 200000, "max_output_tokens": 4096, "supports_tools": false, "supports_vision": true, "supports_streaming": true, "supports_json_mode": false, "supports_function_calling": false, "temperature_range": [0.0, 2.0], "top_p_range": [0.0, 1.0]}, "status": "available", "created_at": "2025-06-25T12:42:55.814772", "updated_at": "2025-06-25T12:42:55.814773", "tags": ["快速", "经济"], "homepage": "https://www.anthropic.com/claude", "documentation": "https://docs.anthropic.com/claude/docs"}, {"id": "gemini-1.5-pro", "name": "Gemini 1.5 Pro", "description": "Google最先进的多模态模型，支持超长上下文", "provider": "Google", "series": "gemini", "categories": ["general", "reasoning", "analysis"], "input_modalities": ["text", "image", "file"], "pricing": {"prompt_price": 0.245, "completion_price": 0.735, "image_price": null, "currency": "CNY"}, "capabilities": {"context_length": 2000000, "max_output_tokens": 8192, "supports_tools": false, "supports_vision": true, "supports_streaming": true, "supports_json_mode": true, "supports_function_calling": false, "temperature_range": [0.0, 2.0], "top_p_range": [0.0, 1.0]}, "status": "available", "created_at": "2025-06-25T12:42:55.816039", "updated_at": "2025-06-25T12:42:55.816041", "tags": ["超长上下文", "多模态"], "homepage": "https://deepmind.google/technologies/gemini/", "documentation": "https://ai.google.dev/docs"}, {"id": "gemini-1.5-flash", "name": "Gemini 1.5 Flash", "description": "快速且高效的模型，平衡性能和成本", "provider": "Google", "series": "gemini", "categories": ["general"], "input_modalities": ["text", "image"], "pricing": {"prompt_price": 0.0525, "completion_price": 0.1575, "image_price": null, "currency": "CNY"}, "capabilities": {"context_length": 1000000, "max_output_tokens": 8192, "supports_tools": false, "supports_vision": true, "supports_streaming": true, "supports_json_mode": false, "supports_function_calling": false, "temperature_range": [0.0, 2.0], "top_p_range": [0.0, 1.0]}, "status": "available", "created_at": "2025-06-25T12:42:55.817216", "updated_at": "2025-06-25T12:42:55.817217", "tags": ["快速", "高效"], "homepage": "https://deepmind.google/technologies/gemini/", "documentation": "https://ai.google.dev/docs"}, {"id": "llama-3.1-405b", "name": "Llama 3.1 405B", "description": "Meta最大的开源模型，具有强大的推理和编程能力", "provider": "Meta", "series": "llama", "categories": ["reasoning", "programming"], "input_modalities": ["text"], "pricing": {"prompt_price": 0.189, "completion_price": 0.756, "image_price": null, "currency": "CNY"}, "capabilities": {"context_length": 131072, "max_output_tokens": 4096, "supports_tools": false, "supports_vision": false, "supports_streaming": true, "supports_json_mode": true, "supports_function_calling": false, "temperature_range": [0.0, 2.0], "top_p_range": [0.0, 1.0]}, "status": "available", "created_at": "2025-06-25T12:42:55.818599", "updated_at": "2025-06-25T12:42:55.818600", "tags": ["开源", "大模型"], "homepage": "https://llama.meta.com/", "documentation": "https://llama.meta.com/docs/"}, {"id": "llama-3.1-70b", "name": "Llama 3.1 70B", "description": "平衡性能和效率的开源模型", "provider": "Meta", "series": "llama", "categories": ["general", "programming"], "input_modalities": ["text"], "pricing": {"prompt_price": 0.0588, "completion_price": 0.0588, "image_price": null, "currency": "CNY"}, "capabilities": {"context_length": 131072, "max_output_tokens": 4096, "supports_tools": false, "supports_vision": false, "supports_streaming": true, "supports_json_mode": false, "supports_function_calling": false, "temperature_range": [0.0, 2.0], "top_p_range": [0.0, 1.0]}, "status": "available", "created_at": "2025-06-25T12:42:55.820121", "updated_at": "2025-06-25T12:42:55.820124", "tags": ["开源", "平衡"], "homepage": "https://llama.meta.com/", "documentation": "https://llama.meta.com/docs/"}, {"id": "qwen-2.5-72b", "name": "<PERSON><PERSON> 2.5 72B", "description": "阿里云通义千问大模型，在中文理解和生成方面表现优秀", "provider": "阿里云", "series": "qwen", "categories": ["general", "translation"], "input_modalities": ["text"], "pricing": {"prompt_price": 0.042, "completion_price": 0.042, "image_price": null, "currency": "CNY"}, "capabilities": {"context_length": 32768, "max_output_tokens": 8192, "supports_tools": false, "supports_vision": false, "supports_streaming": true, "supports_json_mode": false, "supports_function_calling": false, "temperature_range": [0.0, 2.0], "top_p_range": [0.0, 1.0]}, "status": "available", "created_at": "2025-06-25T12:42:55.821839", "updated_at": "2025-06-25T12:42:55.821840", "tags": ["中文", "通义千问"], "homepage": "https://tongyi.aliyun.com/", "documentation": "https://help.aliyun.com/zh/dashscope/"}, {"id": "chatglm-4", "name": "ChatGLM-4", "description": "智谱AI的对话语言模型，支持中英文对话", "provider": "智谱AI", "series": "chatglm", "categories": ["general", "translation"], "input_modalities": ["text"], "pricing": {"prompt_price": 0.07, "completion_price": 0.07, "image_price": null, "currency": "CNY"}, "capabilities": {"context_length": 128000, "max_output_tokens": 4096, "supports_tools": true, "supports_vision": false, "supports_streaming": true, "supports_json_mode": false, "supports_function_calling": false, "temperature_range": [0.0, 2.0], "top_p_range": [0.0, 1.0]}, "status": "available", "created_at": "2025-06-25T12:42:55.823371", "updated_at": "2025-06-25T12:42:55.823373", "tags": ["中文", "对话"], "homepage": "https://www.zhipuai.cn/", "documentation": "https://open.bigmodel.cn/dev/api"}], "updated_at": "2025-06-25T12:42:55.823959"}