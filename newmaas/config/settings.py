"""
NewMaaS 配置设置
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "NewMaaS"
    VERSION: str = "2.0.0"
    DEBUG: bool = False
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # CORS 配置
    ALLOWED_ORIGINS: List[str] = ["*"]
    
    # GPU 监控配置
    GPU_MONITOR_INTERVAL: int = 10  # 秒
    VGPU_MONITOR_INTERVAL: int = 15  # 秒
    MODEL_MONITOR_INTERVAL: int = 30  # 秒
    
    # 系统监控配置
    SYSTEM_MONITOR_INTERVAL: int = 5  # 秒
    
    # MaaS 服务配置
    MAAS_ENABLED: bool = True
    DEFAULT_RATE_LIMIT: int = 1000  # 每小时请求数
    API_KEY_LENGTH: int = 64
    
    # 模型配置
    MODEL_CONFIG_PATH: str = "config/models.json"
    MODEL_CACHE_SIZE: int = 100
    
    # vGPU 配置
    VGPU_CONFIG_PATH: str = "config/vgpu.json"
    MAX_VGPU_PER_GPU: int = 8
    MIN_VGPU_MEMORY: int = 512  # MB
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 数据库配置（可选）
    DATABASE_URL: Optional[str] = None
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: Optional[str] = None
    
    # 货币配置
    CURRENCY: str = "CNY"  # 人民币
    CURRENCY_SYMBOL: str = "¥"
    
    # OpenAI 兼容 API 配置
    OPENAI_API_BASE: str = "/v1"
    OPENAI_MODEL_PREFIX: str = "newmaas-"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 创建全局设置实例
settings = Settings()


# 模型类型配置
MODEL_TYPES = {
    "llm": "大语言模型",
    "embedding": "嵌入模型", 
    "image": "图像模型",
    "audio": "音频模型",
    "multimodal": "多模态模型"
}

# 支持的框架
SUPPORTED_FRAMEWORKS = {
    "ollama": "Ollama",
    "transformers": "Transformers",
    "vllm": "vLLM",
    "tensorrt": "TensorRT",
    "onnx": "ONNX",
    "custom": "自定义"
}

# GPU 架构配置
GPU_ARCHITECTURES = {
    "ampere": ["RTX 30", "RTX 40", "A100", "A6000"],
    "turing": ["RTX 20", "GTX 16", "T4"],
    "pascal": ["GTX 10", "P100", "P40"],
    "volta": ["V100"],
    "ada": ["RTX 40"]
}

# 默认定价配置（人民币/千tokens）
DEFAULT_PRICING = {
    "input_tokens": 0.001,  # ¥0.001/1K tokens
    "output_tokens": 0.002,  # ¥0.002/1K tokens
    "image_generation": 0.1,  # ¥0.1/image
    "audio_transcription": 0.01  # ¥0.01/minute
}
