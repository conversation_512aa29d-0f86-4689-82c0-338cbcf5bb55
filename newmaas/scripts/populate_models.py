"""
预置模型数据脚本 - 添加常见的大模型到模型库
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.model_library import (
    ModelLibrary, ModelInfo, ModelPricing, ModelCapabilities,
    ModelCategory, ModelSeries, InputModality, ModelStatus
)


async def populate_models():
    """填充预置模型数据"""
    library = ModelLibrary()
    await library.initialize()
    
    # GPT 系列模型
    gpt_models = [
        {
            "id": "gpt-4o",
            "name": "GPT-4o",
            "description": "OpenAI最新的多模态模型，支持文本和图像输入，具有强大的推理能力",
            "provider": "OpenAI",
            "series": ModelSeries.GPT,
            "categories": [ModelCategory.GENERAL, ModelCategory.REASONING, ModelCategory.PROGRAMMING],
            "input_modalities": [InputModality.TEXT, InputModality.IMAGE],
            "pricing": ModelPricing(prompt_price=0.035, completion_price=0.105),
            "capabilities": ModelCapabilities(
                context_length=128000,
                max_output_tokens=4096,
                supports_tools=True,
                supports_vision=True,
                supports_streaming=True,
                supports_json_mode=True,
                supports_function_calling=True
            ),
            "tags": ["最新", "多模态", "推理"],
            "homepage": "https://openai.com/gpt-4",
            "documentation": "https://platform.openai.com/docs/models/gpt-4"
        },
        {
            "id": "gpt-4-turbo",
            "name": "GPT-4 Turbo",
            "description": "GPT-4的优化版本，更快的响应速度和更低的成本",
            "provider": "OpenAI",
            "series": ModelSeries.GPT,
            "categories": [ModelCategory.GENERAL, ModelCategory.PROGRAMMING, ModelCategory.CREATIVE],
            "input_modalities": [InputModality.TEXT, InputModality.IMAGE],
            "pricing": ModelPricing(prompt_price=0.07, completion_price=0.21),
            "capabilities": ModelCapabilities(
                context_length=128000,
                max_output_tokens=4096,
                supports_tools=True,
                supports_vision=True,
                supports_streaming=True,
                supports_json_mode=True,
                supports_function_calling=True
            ),
            "tags": ["快速", "多模态"],
            "homepage": "https://openai.com/gpt-4",
            "documentation": "https://platform.openai.com/docs/models/gpt-4"
        },
        {
            "id": "gpt-3.5-turbo",
            "name": "GPT-3.5 Turbo",
            "description": "经济实惠的对话模型，适合大多数应用场景",
            "provider": "OpenAI",
            "series": ModelSeries.GPT,
            "categories": [ModelCategory.GENERAL, ModelCategory.CREATIVE],
            "input_modalities": [InputModality.TEXT],
            "pricing": ModelPricing(prompt_price=0.0035, completion_price=0.0105),
            "capabilities": ModelCapabilities(
                context_length=16385,
                max_output_tokens=4096,
                supports_tools=True,
                supports_streaming=True,
                supports_json_mode=True,
                supports_function_calling=True
            ),
            "tags": ["经济", "对话"],
            "homepage": "https://openai.com/gpt-3-5",
            "documentation": "https://platform.openai.com/docs/models/gpt-3-5"
        }
    ]
    
    # Claude 系列模型
    claude_models = [
        {
            "id": "claude-3-5-sonnet",
            "name": "Claude 3.5 Sonnet",
            "description": "Anthropic最强大的模型，在推理、数学和编程方面表现卓越",
            "provider": "Anthropic",
            "series": ModelSeries.CLAUDE,
            "categories": [ModelCategory.REASONING, ModelCategory.PROGRAMMING, ModelCategory.ANALYSIS],
            "input_modalities": [InputModality.TEXT, InputModality.IMAGE],
            "pricing": ModelPricing(prompt_price=0.21, completion_price=1.05),
            "capabilities": ModelCapabilities(
                context_length=200000,
                max_output_tokens=8192,
                supports_vision=True,
                supports_streaming=True,
                supports_json_mode=True
            ),
            "tags": ["推理", "编程", "分析"],
            "homepage": "https://www.anthropic.com/claude",
            "documentation": "https://docs.anthropic.com/claude/docs"
        },
        {
            "id": "claude-3-haiku",
            "name": "Claude 3 Haiku",
            "description": "快速且经济的模型，适合简单任务和高频调用",
            "provider": "Anthropic",
            "series": ModelSeries.CLAUDE,
            "categories": [ModelCategory.GENERAL],
            "input_modalities": [InputModality.TEXT, InputModality.IMAGE],
            "pricing": ModelPricing(prompt_price=0.0175, completion_price=0.0875),
            "capabilities": ModelCapabilities(
                context_length=200000,
                max_output_tokens=4096,
                supports_vision=True,
                supports_streaming=True
            ),
            "tags": ["快速", "经济"],
            "homepage": "https://www.anthropic.com/claude",
            "documentation": "https://docs.anthropic.com/claude/docs"
        }
    ]
    
    # Gemini 系列模型
    gemini_models = [
        {
            "id": "gemini-1.5-pro",
            "name": "Gemini 1.5 Pro",
            "description": "Google最先进的多模态模型，支持超长上下文",
            "provider": "Google",
            "series": ModelSeries.GEMINI,
            "categories": [ModelCategory.GENERAL, ModelCategory.REASONING, ModelCategory.ANALYSIS],
            "input_modalities": [InputModality.TEXT, InputModality.IMAGE, InputModality.FILE],
            "pricing": ModelPricing(prompt_price=0.245, completion_price=0.735),
            "capabilities": ModelCapabilities(
                context_length=2000000,
                max_output_tokens=8192,
                supports_vision=True,
                supports_streaming=True,
                supports_json_mode=True
            ),
            "tags": ["超长上下文", "多模态"],
            "homepage": "https://deepmind.google/technologies/gemini/",
            "documentation": "https://ai.google.dev/docs"
        },
        {
            "id": "gemini-1.5-flash",
            "name": "Gemini 1.5 Flash",
            "description": "快速且高效的模型，平衡性能和成本",
            "provider": "Google",
            "series": ModelSeries.GEMINI,
            "categories": [ModelCategory.GENERAL],
            "input_modalities": [InputModality.TEXT, InputModality.IMAGE],
            "pricing": ModelPricing(prompt_price=0.0525, completion_price=0.1575),
            "capabilities": ModelCapabilities(
                context_length=1000000,
                max_output_tokens=8192,
                supports_vision=True,
                supports_streaming=True
            ),
            "tags": ["快速", "高效"],
            "homepage": "https://deepmind.google/technologies/gemini/",
            "documentation": "https://ai.google.dev/docs"
        }
    ]
    
    # Llama 系列模型
    llama_models = [
        {
            "id": "llama-3.1-405b",
            "name": "Llama 3.1 405B",
            "description": "Meta最大的开源模型，具有强大的推理和编程能力",
            "provider": "Meta",
            "series": ModelSeries.LLAMA,
            "categories": [ModelCategory.REASONING, ModelCategory.PROGRAMMING],
            "input_modalities": [InputModality.TEXT],
            "pricing": ModelPricing(prompt_price=0.189, completion_price=0.756),
            "capabilities": ModelCapabilities(
                context_length=131072,
                max_output_tokens=4096,
                supports_streaming=True,
                supports_json_mode=True
            ),
            "tags": ["开源", "大模型"],
            "homepage": "https://llama.meta.com/",
            "documentation": "https://llama.meta.com/docs/"
        },
        {
            "id": "llama-3.1-70b",
            "name": "Llama 3.1 70B",
            "description": "平衡性能和效率的开源模型",
            "provider": "Meta",
            "series": ModelSeries.LLAMA,
            "categories": [ModelCategory.GENERAL, ModelCategory.PROGRAMMING],
            "input_modalities": [InputModality.TEXT],
            "pricing": ModelPricing(prompt_price=0.0588, completion_price=0.0588),
            "capabilities": ModelCapabilities(
                context_length=131072,
                max_output_tokens=4096,
                supports_streaming=True
            ),
            "tags": ["开源", "平衡"],
            "homepage": "https://llama.meta.com/",
            "documentation": "https://llama.meta.com/docs/"
        }
    ]
    
    # 中文模型
    chinese_models = [
        {
            "id": "qwen-2.5-72b",
            "name": "Qwen 2.5 72B",
            "description": "阿里云通义千问大模型，在中文理解和生成方面表现优秀",
            "provider": "阿里云",
            "series": ModelSeries.QWEN,
            "categories": [ModelCategory.GENERAL, ModelCategory.TRANSLATION],
            "input_modalities": [InputModality.TEXT],
            "pricing": ModelPricing(prompt_price=0.042, completion_price=0.042),
            "capabilities": ModelCapabilities(
                context_length=32768,
                max_output_tokens=8192,
                supports_streaming=True
            ),
            "tags": ["中文", "通义千问"],
            "homepage": "https://tongyi.aliyun.com/",
            "documentation": "https://help.aliyun.com/zh/dashscope/"
        },
        {
            "id": "chatglm-4",
            "name": "ChatGLM-4",
            "description": "智谱AI的对话语言模型，支持中英文对话",
            "provider": "智谱AI",
            "series": ModelSeries.CHATGLM,
            "categories": [ModelCategory.GENERAL, ModelCategory.TRANSLATION],
            "input_modalities": [InputModality.TEXT],
            "pricing": ModelPricing(prompt_price=0.07, completion_price=0.07),
            "capabilities": ModelCapabilities(
                context_length=128000,
                max_output_tokens=4096,
                supports_streaming=True,
                supports_tools=True
            ),
            "tags": ["中文", "对话"],
            "homepage": "https://www.zhipuai.cn/",
            "documentation": "https://open.bigmodel.cn/dev/api"
        }
    ]
    
    # 添加所有模型
    all_models = gpt_models + claude_models + gemini_models + llama_models + chinese_models
    
    for model_data in all_models:
        try:
            model = ModelInfo(**model_data)
            success = await library.add_model(model)
            if success:
                print(f"✅ 添加模型成功: {model.name}")
            else:
                print(f"❌ 添加模型失败: {model.name}")
        except Exception as e:
            print(f"❌ 添加模型出错: {model_data.get('name', 'Unknown')} - {e}")
    
    print(f"\n🎉 模型库初始化完成，共添加 {len(all_models)} 个模型")
    
    # 显示统计信息
    stats = await library.get_statistics()
    print(f"📊 统计信息:")
    print(f"   总模型数: {stats['total_models']}")
    print(f"   系列分布: {stats['series_distribution']}")
    print(f"   分类分布: {stats['category_distribution']}")
    
    await library.cleanup()


if __name__ == "__main__":
    asyncio.run(populate_models())
