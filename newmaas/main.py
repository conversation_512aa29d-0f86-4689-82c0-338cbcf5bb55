"""
NewMaaS - 新一代多模型管理平台
基于 FastAPI 构建的高性能 GPU 多模型管理系统
"""

import uvicorn
from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
from pathlib import Path

from config.settings import settings
from core.gpu_detector import GPUDetector
from core.vgpu_manager import VGPUManager
from core.model_manager import ModelManager
from core.maas_service import MAASService
from api import gpu, models, vgpu, maas, system

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局管理器实例
gpu_detector = None
vgpu_manager = None
model_manager = None
maas_service = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global gpu_detector, vgpu_manager, model_manager, maas_service
    
    logger.info("🚀 启动 NewMaaS 多模型管理平台...")
    
    try:
        # 初始化核心组件
        gpu_detector = GPUDetector()
        await gpu_detector.initialize()
        
        vgpu_manager = VGPUManager(gpu_detector)
        await vgpu_manager.initialize()
        
        model_manager = ModelManager()
        await model_manager.initialize()
        
        maas_service = MAASService(model_manager)
        await maas_service.initialize()
        
        # 将管理器实例添加到应用状态
        app.state.gpu_detector = gpu_detector
        app.state.vgpu_manager = vgpu_manager
        app.state.model_manager = model_manager
        app.state.maas_service = maas_service
        
        logger.info("✅ NewMaaS 平台初始化完成")
        
    except Exception as e:
        logger.error(f"❌ 平台初始化失败: {e}")
        raise
    
    yield
    
    # 清理资源
    logger.info("🔄 正在关闭 NewMaaS 平台...")
    
    if maas_service:
        await maas_service.cleanup()
    if model_manager:
        await model_manager.cleanup()
    if vgpu_manager:
        await vgpu_manager.cleanup()
    if gpu_detector:
        await gpu_detector.cleanup()
    
    logger.info("✅ NewMaaS 平台已关闭")


# 创建 FastAPI 应用
app = FastAPI(
    title="NewMaaS - 多模型管理平台",
    description="基于 FastAPI 的高性能 GPU 多模型管理系统",
    version="2.0.0",
    lifespan=lifespan
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 配置模板
templates = Jinja2Templates(directory="templates")

# 注册 API 路由
app.include_router(gpu.router, prefix="/api/gpu", tags=["GPU管理"])
app.include_router(models.router, prefix="/api/models", tags=["模型管理"])
app.include_router(vgpu.router, prefix="/api/vgpu", tags=["vGPU管理"])
app.include_router(maas.router, prefix="/api/maas", tags=["MaaS服务"])
app.include_router(system.router, prefix="/api/system", tags=["系统信息"])

# OpenAI 兼容 API
app.include_router(maas.openai_router, prefix="/v1", tags=["OpenAI兼容API"])


@app.get("/")
async def dashboard(request: Request):
    """主仪表板页面"""
    return templates.TemplateResponse("dashboard.html", {"request": request})


@app.get("/maas")
async def maas_management(request: Request):
    """MaaS 管理页面"""
    return templates.TemplateResponse("maas_management.html", {"request": request})


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "platform": "NewMaaS"
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
