"""
模型库管理 API 路由
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Dict, Any, Optional
import logging

from core.model_library import (
    ModelLibrary, ModelInfo, ModelFilter, ModelCategory, 
    ModelSeries, InputModality, ModelStatus, ModelPricing, ModelCapabilities
)

logger = logging.getLogger(__name__)

router = APIRouter()


def get_model_library():
    """获取模型库实例"""
    from main import model_library
    if not model_library:
        raise HTTPException(status_code=503, detail="模型库未初始化")
    return model_library


@router.get("/models", summary="获取模型列表")
async def list_models(
    # 筛选参数
    categories: Optional[List[str]] = Query(None, description="模型分类"),
    series: Optional[List[str]] = Query(None, description="模型系列"),
    input_modalities: Optional[List[str]] = Query(None, description="输入模态"),
    status: Optional[List[str]] = Query(None, description="模型状态"),
    min_context_length: Optional[int] = Query(None, description="最小上下文长度"),
    max_context_length: Optional[int] = Query(None, description="最大上下文长度"),
    min_price: Optional[float] = Query(None, description="最低价格"),
    max_price: Optional[float] = Query(None, description="最高价格"),
    supports_tools: Optional[bool] = Query(None, description="支持工具调用"),
    supports_vision: Optional[bool] = Query(None, description="支持视觉"),
    supports_streaming: Optional[bool] = Query(None, description="支持流式输出"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    tags: Optional[List[str]] = Query(None, description="标签"),
    # 排序参数
    sort_by: Optional[str] = Query("name", description="排序字段"),
    sort_order: Optional[str] = Query("asc", description="排序顺序"),
    # 分页参数
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    library: ModelLibrary = Depends(get_model_library)
) -> Dict[str, Any]:
    """获取模型列表，支持筛选、搜索、排序和分页"""
    try:
        # 构建筛选条件
        filter_params = ModelFilter(
            categories=[ModelCategory(cat) for cat in categories] if categories else None,
            series=[ModelSeries(s) for s in series] if series else None,
            input_modalities=[InputModality(mod) for mod in input_modalities] if input_modalities else None,
            status=[ModelStatus(st) for st in status] if status else None,
            min_context_length=min_context_length,
            max_context_length=max_context_length,
            min_price=min_price,
            max_price=max_price,
            supports_tools=supports_tools,
            supports_vision=supports_vision,
            supports_streaming=supports_streaming,
            search_query=search,
            tags=tags
        )
        
        # 获取筛选后的模型列表
        models = await library.list_models(filter_params)
        
        # 排序
        if sort_by == "name":
            models.sort(key=lambda x: x.name, reverse=(sort_order == "desc"))
        elif sort_by == "price":
            models.sort(key=lambda x: x.pricing.prompt_price, reverse=(sort_order == "desc"))
        elif sort_by == "context_length":
            models.sort(key=lambda x: x.capabilities.context_length, reverse=(sort_order == "desc"))
        elif sort_by == "created_at":
            models.sort(key=lambda x: x.created_at, reverse=(sort_order == "desc"))
        elif sort_by == "updated_at":
            models.sort(key=lambda x: x.updated_at, reverse=(sort_order == "desc"))
        
        # 分页
        total = len(models)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_models = models[start_idx:end_idx]
        
        return {
            "success": True,
            "data": {
                "models": [model.to_dict() for model in paginated_models],
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total,
                    "total_pages": (total + page_size - 1) // page_size
                }
            }
        }
        
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@router.get("/models/{model_id}", summary="获取模型详情")
async def get_model(
    model_id: str,
    library: ModelLibrary = Depends(get_model_library)
) -> Dict[str, Any]:
    """获取指定模型的详细信息"""
    try:
        model = await library.get_model(model_id)
        if not model:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        return {
            "success": True,
            "data": model.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型详情失败: {str(e)}")


@router.post("/models", summary="添加模型")
async def add_model(
    model_data: Dict[str, Any],
    library: ModelLibrary = Depends(get_model_library)
) -> Dict[str, Any]:
    """添加新模型到模型库"""
    try:
        # 验证必需字段
        required_fields = ['id', 'name', 'description', 'provider', 'series', 'categories', 'input_modalities', 'pricing', 'capabilities']
        for field in required_fields:
            if field not in model_data:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 构建模型对象
        pricing = ModelPricing(**model_data['pricing'])
        capabilities = ModelCapabilities(**model_data['capabilities'])
        
        model = ModelInfo(
            id=model_data['id'],
            name=model_data['name'],
            description=model_data['description'],
            provider=model_data['provider'],
            series=ModelSeries(model_data['series']),
            categories=[ModelCategory(cat) for cat in model_data['categories']],
            input_modalities=[InputModality(mod) for mod in model_data['input_modalities']],
            pricing=pricing,
            capabilities=capabilities,
            status=ModelStatus(model_data.get('status', 'available')),
            tags=model_data.get('tags', []),
            homepage=model_data.get('homepage'),
            documentation=model_data.get('documentation')
        )
        
        success = await library.add_model(model)
        if not success:
            raise HTTPException(status_code=400, detail="添加模型失败")
        
        return {
            "success": True,
            "message": "模型添加成功",
            "data": model.to_dict()
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"添加模型失败: {e}")
        raise HTTPException(status_code=500, detail=f"添加模型失败: {str(e)}")


@router.put("/models/{model_id}", summary="更新模型")
async def update_model(
    model_id: str,
    updates: Dict[str, Any],
    library: ModelLibrary = Depends(get_model_library)
) -> Dict[str, Any]:
    """更新指定模型的信息"""
    try:
        success = await library.update_model(model_id, updates)
        if not success:
            raise HTTPException(status_code=400, detail="更新模型失败")
        
        # 获取更新后的模型
        model = await library.get_model(model_id)
        
        return {
            "success": True,
            "message": "模型更新成功",
            "data": model.to_dict() if model else None
        }
        
    except Exception as e:
        logger.error(f"更新模型失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新模型失败: {str(e)}")


@router.delete("/models/{model_id}", summary="删除模型")
async def delete_model(
    model_id: str,
    library: ModelLibrary = Depends(get_model_library)
) -> Dict[str, Any]:
    """从模型库中删除指定模型"""
    try:
        success = await library.delete_model(model_id)
        if not success:
            raise HTTPException(status_code=400, detail="删除模型失败")
        
        return {
            "success": True,
            "message": "模型删除成功"
        }
        
    except Exception as e:
        logger.error(f"删除模型失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除模型失败: {str(e)}")


@router.get("/categories", summary="获取所有分类")
async def get_categories(
    library: ModelLibrary = Depends(get_model_library)
) -> Dict[str, Any]:
    """获取所有模型分类"""
    try:
        categories = await library.get_categories()
        
        return {
            "success": True,
            "data": categories
        }
        
    except Exception as e:
        logger.error(f"获取分类失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取分类失败: {str(e)}")


@router.get("/series", summary="获取所有系列")
async def get_series(
    library: ModelLibrary = Depends(get_model_library)
) -> Dict[str, Any]:
    """获取所有模型系列"""
    try:
        series = await library.get_series()
        
        return {
            "success": True,
            "data": series
        }
        
    except Exception as e:
        logger.error(f"获取系列失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系列失败: {str(e)}")


@router.get("/providers", summary="获取所有提供商")
async def get_providers(
    library: ModelLibrary = Depends(get_model_library)
) -> Dict[str, Any]:
    """获取所有模型提供商"""
    try:
        providers = await library.get_providers()
        
        return {
            "success": True,
            "data": providers
        }
        
    except Exception as e:
        logger.error(f"获取提供商失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取提供商失败: {str(e)}")


@router.get("/tags", summary="获取所有标签")
async def get_tags(
    library: ModelLibrary = Depends(get_model_library)
) -> Dict[str, Any]:
    """获取所有模型标签"""
    try:
        tags = await library.get_tags()
        
        return {
            "success": True,
            "data": tags
        }
        
    except Exception as e:
        logger.error(f"获取标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取标签失败: {str(e)}")


@router.get("/statistics", summary="获取模型库统计")
async def get_statistics(
    library: ModelLibrary = Depends(get_model_library)
) -> Dict[str, Any]:
    """获取模型库统计信息"""
    try:
        stats = await library.get_statistics()
        
        return {
            "success": True,
            "data": stats
        }
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/enums", summary="获取枚举值")
async def get_enums() -> Dict[str, Any]:
    """获取所有枚举值，用于前端选择器"""
    try:
        return {
            "success": True,
            "data": {
                "categories": [{"value": cat.value, "label": cat.value} for cat in ModelCategory],
                "series": [{"value": s.value, "label": s.value} for s in ModelSeries],
                "input_modalities": [{"value": mod.value, "label": mod.value} for mod in InputModality],
                "status": [{"value": st.value, "label": st.value} for st in ModelStatus]
            }
        }
        
    except Exception as e:
        logger.error(f"获取枚举值失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取枚举值失败: {str(e)}")
