"""
系统信息 API 路由
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import logging
import platform
import psutil
from datetime import datetime

logger = logging.getLogger(__name__)

router = APIRouter()


def get_gpu_detector():
    """获取GPU检测器实例"""
    from main import gpu_detector
    if not gpu_detector:
        raise HTTPException(status_code=503, detail="GPU检测器未初始化")
    return gpu_detector


def get_model_manager():
    """获取模型管理器实例"""
    from main import model_manager
    if not model_manager:
        raise HTTPException(status_code=503, detail="模型管理器未初始化")
    return model_manager


def get_vgpu_manager():
    """获取vGPU管理器实例"""
    from main import vgpu_manager
    if not vgpu_manager:
        raise HTTPException(status_code=503, detail="vGPU管理器未初始化")
    return vgpu_manager


def get_maas_service():
    """获取MaaS服务实例"""
    from main import maas_service
    if not maas_service:
        raise HTTPException(status_code=503, detail="MaaS服务未初始化")
    return maas_service


@router.get("/info", summary="获取系统信息")
async def get_system_info(detector=Depends(get_gpu_detector)) -> Dict[str, Any]:
    """获取系统基本信息"""
    try:
        system_info = await detector.get_system_info()
        
        return {
            "success": True,
            "data": system_info.to_dict() if system_info else None
        }
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")


@router.get("/status", summary="获取系统状态")
async def get_system_status(
    gpu_detector=Depends(get_gpu_detector),
    model_manager=Depends(get_model_manager),
    vgpu_manager=Depends(get_vgpu_manager),
    maas_service=Depends(get_maas_service)
) -> Dict[str, Any]:
    """获取系统整体状态"""
    try:
        # 获取各组件状态
        gpu_info = await gpu_detector.get_all_gpu_info()
        system_info = await gpu_detector.get_system_info()
        
        # 统计信息
        total_models = model_manager.get_model_count()
        total_instances = model_manager.get_instance_count()
        total_vgpus = len(vgpu_manager.vgpu_instances)
        total_api_keys = maas_service.get_api_key_count()
        active_api_keys = maas_service.get_active_api_key_count()
        
        # 系统资源使用情况
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        status = {
            "platform": {
                "name": "NewMaaS",
                "version": "2.0.0",
                "uptime": datetime.now().isoformat(),
                "python_version": platform.python_version(),
                "platform": platform.platform()
            },
            "hardware": {
                "cpu": {
                    "usage_percent": cpu_percent,
                    "count": psutil.cpu_count(),
                    "frequency": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "usage_percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "usage_percent": (disk.used / disk.total * 100) if disk.total > 0 else 0
                },
                "gpu": {
                    "count": len(gpu_info),
                    "total_memory": sum(gpu.memory_total for gpu in gpu_info),
                    "used_memory": sum(gpu.memory_used for gpu in gpu_info),
                    "average_utilization": sum(gpu.utilization for gpu in gpu_info) / len(gpu_info) if gpu_info else 0
                }
            },
            "services": {
                "gpu_monitoring": gpu_detector.is_monitoring(),
                "vgpu_monitoring": vgpu_manager.is_monitoring(),
                "model_monitoring": model_manager.is_monitoring(),
                "maas_service": True  # MaaS服务总是运行的
            },
            "statistics": {
                "models": {
                    "total": total_models,
                    "loaded_instances": total_instances
                },
                "vgpu": {
                    "total": total_vgpus,
                    "active": len([v for v in vgpu_manager.vgpu_instances.values() 
                                 if v.status.value == "running"])
                },
                "api_keys": {
                    "total": total_api_keys,
                    "active": active_api_keys
                }
            }
        }
        
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


@router.get("/health", summary="系统健康检查")
async def system_health_check(
    gpu_detector=Depends(get_gpu_detector),
    model_manager=Depends(get_model_manager),
    vgpu_manager=Depends(get_vgpu_manager),
    maas_service=Depends(get_maas_service)
) -> Dict[str, Any]:
    """系统健康检查"""
    try:
        health_status = {
            "overall_healthy": True,
            "components": {},
            "issues": [],
            "timestamp": datetime.now().isoformat()
        }
        
        # 检查GPU状态
        try:
            gpu_info = await gpu_detector.get_all_gpu_info()
            gpu_healthy = all(gpu.temperature < 85 for gpu in gpu_info)
            health_status["components"]["gpu"] = {
                "healthy": gpu_healthy,
                "monitoring": gpu_detector.is_monitoring(),
                "count": len(gpu_info)
            }
            if not gpu_healthy:
                health_status["overall_healthy"] = False
                health_status["issues"].append("GPU温度过高")
        except Exception as e:
            health_status["components"]["gpu"] = {"healthy": False, "error": str(e)}
            health_status["overall_healthy"] = False
            health_status["issues"].append("GPU检测失败")
        
        # 检查模型管理器状态
        try:
            model_healthy = model_manager.is_monitoring()
            health_status["components"]["model_manager"] = {
                "healthy": model_healthy,
                "monitoring": model_healthy,
                "model_count": model_manager.get_model_count(),
                "instance_count": model_manager.get_instance_count()
            }
        except Exception as e:
            health_status["components"]["model_manager"] = {"healthy": False, "error": str(e)}
            health_status["overall_healthy"] = False
            health_status["issues"].append("模型管理器异常")
        
        # 检查vGPU管理器状态
        try:
            vgpu_healthy = vgpu_manager.is_monitoring()
            health_status["components"]["vgpu_manager"] = {
                "healthy": vgpu_healthy,
                "monitoring": vgpu_healthy,
                "vgpu_count": len(vgpu_manager.vgpu_instances)
            }
        except Exception as e:
            health_status["components"]["vgpu_manager"] = {"healthy": False, "error": str(e)}
            health_status["overall_healthy"] = False
            health_status["issues"].append("vGPU管理器异常")
        
        # 检查MaaS服务状态
        try:
            maas_healthy = True  # MaaS服务基本检查
            health_status["components"]["maas_service"] = {
                "healthy": maas_healthy,
                "api_key_count": maas_service.get_api_key_count(),
                "active_keys": maas_service.get_active_api_key_count()
            }
        except Exception as e:
            health_status["components"]["maas_service"] = {"healthy": False, "error": str(e)}
            health_status["overall_healthy"] = False
            health_status["issues"].append("MaaS服务异常")
        
        # 检查系统资源
        try:
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            memory_healthy = memory.percent < 90
            disk_healthy = (disk.used / disk.total * 100) < 90
            
            health_status["components"]["system_resources"] = {
                "healthy": memory_healthy and disk_healthy,
                "memory_usage": memory.percent,
                "disk_usage": (disk.used / disk.total * 100)
            }
            
            if not memory_healthy:
                health_status["overall_healthy"] = False
                health_status["issues"].append("内存使用率过高")
            
            if not disk_healthy:
                health_status["overall_healthy"] = False
                health_status["issues"].append("磁盘使用率过高")
                
        except Exception as e:
            health_status["components"]["system_resources"] = {"healthy": False, "error": str(e)}
            health_status["overall_healthy"] = False
            health_status["issues"].append("系统资源检查失败")
        
        return {
            "success": True,
            "data": health_status
        }
    except Exception as e:
        logger.error(f"系统健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"系统健康检查失败: {str(e)}")


@router.get("/metrics", summary="获取系统指标")
async def get_system_metrics(detector=Depends(get_gpu_detector)) -> Dict[str, Any]:
    """获取系统性能指标"""
    try:
        # CPU指标
        cpu_percent = psutil.cpu_percent(interval=1, percpu=True)
        cpu_freq = psutil.cpu_freq()
        
        # 内存指标
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # 磁盘指标
        disk = psutil.disk_usage('/')
        disk_io = psutil.disk_io_counters()
        
        # 网络指标
        network_io = psutil.net_io_counters()
        
        # GPU指标
        gpu_info = await detector.get_all_gpu_info()
        
        metrics = {
            "cpu": {
                "usage_percent": sum(cpu_percent) / len(cpu_percent),
                "usage_per_core": cpu_percent,
                "frequency": cpu_freq._asdict() if cpu_freq else None,
                "count": psutil.cpu_count()
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "usage_percent": memory.percent,
                "swap_total": swap.total,
                "swap_used": swap.used,
                "swap_percent": swap.percent
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "usage_percent": (disk.used / disk.total * 100) if disk.total > 0 else 0,
                "io": disk_io._asdict() if disk_io else None
            },
            "network": {
                "bytes_sent": network_io.bytes_sent,
                "bytes_recv": network_io.bytes_recv,
                "packets_sent": network_io.packets_sent,
                "packets_recv": network_io.packets_recv
            },
            "gpu": [
                {
                    "gpu_id": gpu.gpu_id,
                    "name": gpu.name,
                    "utilization": gpu.utilization,
                    "memory_total": gpu.memory_total,
                    "memory_used": gpu.memory_used,
                    "memory_free": gpu.memory_free,
                    "temperature": gpu.temperature,
                    "power_usage": gpu.power_usage
                }
                for gpu in gpu_info
            ],
            "timestamp": datetime.now().isoformat()
        }
        
        return {
            "success": True,
            "data": metrics
        }
    except Exception as e:
        logger.error(f"获取系统指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统指标失败: {str(e)}")


@router.get("/version", summary="获取版本信息")
async def get_version_info() -> Dict[str, Any]:
    """获取系统版本信息"""
    try:
        from config.settings import settings
        
        version_info = {
            "platform": "NewMaaS",
            "version": "2.0.0",
            "api_version": "v1",
            "build_date": "2024-12-19",
            "python_version": platform.python_version(),
            "platform_info": platform.platform(),
            "architecture": platform.architecture(),
            "settings": {
                "host": settings.HOST,
                "port": settings.PORT,
                "debug": settings.DEBUG,
                "currency": settings.CURRENCY
            }
        }
        
        return {
            "success": True,
            "data": version_info
        }
    except Exception as e:
        logger.error(f"获取版本信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取版本信息失败: {str(e)}")
