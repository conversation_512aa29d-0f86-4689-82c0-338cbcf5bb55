"""
MaaS 服务 API 路由
包括API密钥管理、使用统计和OpenAI兼容API
"""

from fastapi import APIRouter, HTTPException, Depends, Header, Request
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import List, Dict, Any, Optional, AsyncGenerator
from fastapi.responses import StreamingResponse
import json
import uuid
import time
import logging

logger = logging.getLogger(__name__)

router = APIRouter()
openai_router = APIRouter()
security = HTTPBearer()


class APIKeyCreateRequest(BaseModel):
    """API密钥创建请求"""
    name: str
    model_access: List[str] = []
    rate_limit: int = 1000
    usage_limit: Optional[float] = None
    expires_days: Optional[int] = None


class APIKeyUpdateRequest(BaseModel):
    """API密钥更新请求"""
    name: Optional[str] = None
    model_access: Optional[List[str]] = None
    rate_limit: Optional[int] = None
    usage_limit: Optional[float] = None
    is_active: Optional[bool] = None


class ChatCompletionRequest(BaseModel):
    """聊天完成请求"""
    model: str
    messages: List[Dict[str, str]]
    max_tokens: Optional[int] = None
    temperature: Optional[float] = 0.7
    top_p: Optional[float] = 1.0
    stream: Optional[bool] = False
    stop: Optional[List[str]] = None


def get_maas_service():
    """获取MaaS服务实例"""
    from main import maas_service
    if not maas_service:
        raise HTTPException(status_code=503, detail="MaaS服务未初始化")
    return maas_service


async def verify_api_key(authorization: str = Header(None)):
    """验证API密钥"""
    if not authorization:
        raise HTTPException(status_code=401, detail="缺少Authorization头")
    
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="无效的Authorization格式")
    
    api_key = authorization[7:]  # 移除 "Bearer " 前缀
    
    maas_service = get_maas_service()
    validated_key = await maas_service.validate_api_key(api_key)
    
    if not validated_key:
        raise HTTPException(status_code=401, detail="无效的API密钥")
    
    return validated_key


# MaaS 管理 API

@router.get("/keys", summary="获取API密钥列表")
async def list_api_keys(service=Depends(get_maas_service)) -> Dict[str, Any]:
    """获取所有API密钥列表"""
    try:
        keys = await service.list_api_keys()
        return {
            "success": True,
            "data": keys
        }
    except Exception as e:
        logger.error(f"获取API密钥列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取API密钥列表失败: {str(e)}")


@router.post("/keys", summary="创建API密钥")
async def create_api_key(
    request: APIKeyCreateRequest,
    service=Depends(get_maas_service)
) -> Dict[str, Any]:
    """创建新的API密钥"""
    try:
        key_id, raw_key = await service.generate_api_key(
            name=request.name,
            model_access=request.model_access,
            rate_limit=request.rate_limit,
            usage_limit=request.usage_limit,
            expires_days=request.expires_days
        )
        
        return {
            "success": True,
            "data": {
                "key_id": key_id,
                "api_key": raw_key,
                "message": "API密钥创建成功，请妥善保存，密钥只显示一次"
            }
        }
    except Exception as e:
        logger.error(f"创建API密钥失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建API密钥失败: {str(e)}")


@router.get("/keys/{key_id}", summary="获取API密钥信息")
async def get_api_key_info(
    key_id: str,
    service=Depends(get_maas_service)
) -> Dict[str, Any]:
    """获取指定API密钥的详细信息"""
    try:
        key_info = await service.get_api_key_info(key_id)
        
        if not key_info:
            raise HTTPException(status_code=404, detail="API密钥不存在")
        
        return {
            "success": True,
            "data": key_info
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取API密钥信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取API密钥信息失败: {str(e)}")


@router.put("/keys/{key_id}", summary="更新API密钥")
async def update_api_key(
    key_id: str,
    request: APIKeyUpdateRequest,
    service=Depends(get_maas_service)
) -> Dict[str, Any]:
    """更新API密钥配置"""
    try:
        # 过滤掉None值
        update_data = {k: v for k, v in request.dict().items() if v is not None}
        
        if not update_data:
            raise HTTPException(status_code=400, detail="没有提供更新数据")
        
        success = await service.update_api_key(key_id, **update_data)
        
        if success:
            return {
                "success": True,
                "message": f"API密钥 {key_id} 更新成功"
            }
        else:
            raise HTTPException(status_code=404, detail="API密钥不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新API密钥失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新API密钥失败: {str(e)}")


@router.post("/keys/{key_id}/revoke", summary="撤销API密钥")
async def revoke_api_key(
    key_id: str,
    service=Depends(get_maas_service)
) -> Dict[str, Any]:
    """撤销指定的API密钥"""
    try:
        success = await service.revoke_api_key(key_id)
        
        if success:
            return {
                "success": True,
                "message": f"API密钥 {key_id} 已撤销"
            }
        else:
            raise HTTPException(status_code=404, detail="API密钥不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"撤销API密钥失败: {e}")
        raise HTTPException(status_code=500, detail=f"撤销API密钥失败: {str(e)}")


@router.delete("/keys/{key_id}", summary="删除API密钥")
async def delete_api_key(
    key_id: str,
    service=Depends(get_maas_service)
) -> Dict[str, Any]:
    """删除指定的API密钥"""
    try:
        success = await service.delete_api_key(key_id)
        
        if success:
            return {
                "success": True,
                "message": f"API密钥 {key_id} 已删除"
            }
        else:
            raise HTTPException(status_code=404, detail="API密钥不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除API密钥失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除API密钥失败: {str(e)}")


@router.get("/usage/{key_id}", summary="获取API密钥使用统计")
async def get_api_key_usage(
    key_id: str,
    days: int = 30,
    service=Depends(get_maas_service)
) -> Dict[str, Any]:
    """获取指定API密钥的使用统计"""
    try:
        usage = await service.get_usage_statistics(key_id, days=days)
        return {
            "success": True,
            "data": usage
        }
    except Exception as e:
        logger.error(f"获取使用统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取使用统计失败: {str(e)}")


@router.get("/usage", summary="获取总体使用统计")
async def get_overall_usage(
    days: int = 30,
    service=Depends(get_maas_service)
) -> Dict[str, Any]:
    """获取总体使用统计"""
    try:
        usage = await service.get_usage_statistics(days=days)
        return {
            "success": True,
            "data": usage
        }
    except Exception as e:
        logger.error(f"获取总体使用统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取总体使用统计失败: {str(e)}")


@router.get("/overview", summary="获取MaaS服务概览")
async def get_maas_overview(service=Depends(get_maas_service)) -> Dict[str, Any]:
    """获取MaaS服务概览统计"""
    try:
        overview = await service.get_overview_stats()
        return {
            "success": True,
            "data": overview
        }
    except Exception as e:
        logger.error(f"获取MaaS概览失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取MaaS概览失败: {str(e)}")


# OpenAI 兼容 API

@openai_router.get("/models", summary="获取可用模型列表")
async def list_available_models(
    api_key=Depends(verify_api_key),
    service=Depends(get_maas_service)
) -> Dict[str, Any]:
    """获取API密钥可访问的模型列表（OpenAI兼容）"""
    try:
        models = await service.get_available_models(api_key)
        
        # 转换为OpenAI格式
        openai_models = []
        for model in models:
            openai_models.append({
                "id": model["id"],
                "object": "model",
                "created": int(time.time()),
                "owned_by": "newmaas",
                "permission": [],
                "root": model["id"],
                "parent": None
            })
        
        return {
            "object": "list",
            "data": openai_models
        }
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@openai_router.post("/chat/completions", summary="聊天完成")
async def chat_completions(
    request: ChatCompletionRequest,
    api_key=Depends(verify_api_key),
    service=Depends(get_maas_service)
) -> Dict[str, Any]:
    """处理聊天完成请求（OpenAI兼容）"""
    try:
        # 检查模型访问权限
        if not await service.check_model_access(api_key, request.model):
            raise HTTPException(status_code=403, detail="无权访问此模型")
        
        # 模拟token计算（实际应用中需要真实计算）
        input_tokens = sum(len(msg.get("content", "").split()) for msg in request.messages)
        output_tokens = 50  # 模拟输出token数
        
        # 处理请求并记录使用量
        request_id = str(uuid.uuid4())
        success = await service.process_request(
            api_key, request.model, input_tokens, output_tokens, request_id
        )
        
        if not success:
            raise HTTPException(status_code=429, detail="请求频率或使用额度超限")
        
        # 模拟响应（实际应用中需要调用真实模型）
        response_content = "这是一个模拟的AI响应。在实际应用中，这里会调用真实的模型进行推理。"
        
        if request.stream:
            # 流式响应
            async def generate_stream():
                chunks = response_content.split()
                for i, chunk in enumerate(chunks):
                    chunk_data = {
                        "id": f"chatcmpl-{request_id}",
                        "object": "chat.completion.chunk",
                        "created": int(time.time()),
                        "model": request.model,
                        "choices": [{
                            "index": 0,
                            "delta": {"content": chunk + " "},
                            "finish_reason": None
                        }]
                    }
                    yield f"data: {json.dumps(chunk_data)}\n\n"
                
                # 结束chunk
                final_chunk = {
                    "id": f"chatcmpl-{request_id}",
                    "object": "chat.completion.chunk",
                    "created": int(time.time()),
                    "model": request.model,
                    "choices": [{
                        "index": 0,
                        "delta": {},
                        "finish_reason": "stop"
                    }]
                }
                yield f"data: {json.dumps(final_chunk)}\n\n"
                yield "data: [DONE]\n\n"
            
            return StreamingResponse(
                generate_stream(),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )
        else:
            # 非流式响应
            return {
                "id": f"chatcmpl-{request_id}",
                "object": "chat.completion",
                "created": int(time.time()),
                "model": request.model,
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": response_content
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": input_tokens,
                    "completion_tokens": output_tokens,
                    "total_tokens": input_tokens + output_tokens
                }
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"聊天完成请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"聊天完成请求失败: {str(e)}")
