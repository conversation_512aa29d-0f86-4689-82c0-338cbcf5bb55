"""
GPU 管理 API 路由
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


def get_gpu_detector():
    """获取GPU检测器实例"""
    from main import gpu_detector
    if not gpu_detector:
        raise HTTPException(status_code=503, detail="GPU检测器未初始化")
    return gpu_detector


@router.get("/info", summary="获取GPU信息")
async def get_gpu_info(detector=Depends(get_gpu_detector)) -> Dict[str, Any]:
    """获取所有GPU的详细信息"""
    try:
        gpu_info = await detector.get_all_gpu_info()
        system_info = await detector.get_system_info()
        
        return {
            "success": True,
            "data": {
                "gpus": [gpu.to_dict() for gpu in gpu_info],
                "system": system_info.to_dict() if system_info else None,
                "summary": {
                    "total_gpus": len(gpu_info),
                    "total_memory": sum(gpu.memory_total for gpu in gpu_info),
                    "total_used_memory": sum(gpu.memory_used for gpu in gpu_info),
                    "average_utilization": sum(gpu.utilization for gpu in gpu_info) / len(gpu_info) if gpu_info else 0
                }
            }
        }
    except Exception as e:
        logger.error(f"获取GPU信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取GPU信息失败: {str(e)}")


@router.get("/status", summary="获取GPU状态")
async def get_gpu_status(detector=Depends(get_gpu_detector)) -> Dict[str, Any]:
    """获取GPU实时状态"""
    try:
        gpu_info = await detector.get_all_gpu_info()

        status_data = []
        for gpu in gpu_info:
            status_data.append({
                "gpu_id": gpu.gpu_id,
                "name": gpu.name,
                "utilization": gpu.utilization,
                "memory_usage": {
                    "used": gpu.memory_used,
                    "total": gpu.memory_total,
                    "free": gpu.memory_free,
                    "usage_percent": (gpu.memory_used / gpu.memory_total * 100) if gpu.memory_total > 0 else 0
                },
                "temperature": gpu.temperature,
                "power_usage": gpu.power_usage,
                "processes": len(gpu.processes),
                "status": "active" if gpu.utilization > 0 else "idle"
            })
        
        return {
            "success": True,
            "data": status_data
        }
    except Exception as e:
        logger.error(f"获取GPU状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取GPU状态失败: {str(e)}")


@router.get("/processes", summary="获取GPU进程信息")
async def get_gpu_processes(detector=Depends(get_gpu_detector)) -> Dict[str, Any]:
    """获取所有GPU上运行的进程信息"""
    try:
        gpu_info = await detector.get_all_gpu_info()

        all_processes = []
        for gpu in gpu_info:
            for process in gpu.processes:
                process_info = process.copy()
                process_info["gpu_id"] = gpu.gpu_id
                process_info["gpu_name"] = gpu.name
                all_processes.append(process_info)
        
        return {
            "success": True,
            "data": {
                "processes": all_processes,
                "total_processes": len(all_processes)
            }
        }
    except Exception as e:
        logger.error(f"获取GPU进程信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取GPU进程信息失败: {str(e)}")


@router.get("/monitoring", summary="获取监控状态")
async def get_monitoring_status(detector=Depends(get_gpu_detector)) -> Dict[str, Any]:
    """获取GPU监控状态"""
    try:
        return {
            "success": True,
            "data": {
                "is_monitoring": detector.is_monitoring(),
                "monitor_interval": detector.monitor_interval,
                "last_update": detector.last_update.isoformat() if detector.last_update else None
            }
        }
    except Exception as e:
        logger.error(f"获取监控状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取监控状态失败: {str(e)}")


@router.post("/monitoring/start", summary="开始GPU监控")
async def start_monitoring(detector=Depends(get_gpu_detector)) -> Dict[str, Any]:
    """开始GPU监控"""
    try:
        await detector.start_monitoring()
        return {
            "success": True,
            "message": "GPU监控已开始"
        }
    except Exception as e:
        logger.error(f"开始GPU监控失败: {e}")
        raise HTTPException(status_code=500, detail=f"开始GPU监控失败: {str(e)}")


@router.post("/monitoring/stop", summary="停止GPU监控")
async def stop_monitoring(detector=Depends(get_gpu_detector)) -> Dict[str, Any]:
    """停止GPU监控"""
    try:
        await detector.stop_monitoring()
        return {
            "success": True,
            "message": "GPU监控已停止"
        }
    except Exception as e:
        logger.error(f"停止GPU监控失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止GPU监控失败: {str(e)}")


@router.get("/capabilities", summary="获取GPU能力信息")
async def get_gpu_capabilities(detector=Depends(get_gpu_detector)) -> Dict[str, Any]:
    """获取GPU计算能力和特性信息"""
    try:
        gpu_info = await detector.get_all_gpu_info()

        capabilities = []
        for gpu in gpu_info:
            capabilities.append({
                "gpu_id": gpu.gpu_id,
                "name": gpu.name,
                "compute_capability": gpu.compute_capability,
                "cuda_version": gpu.cuda_version,
                "memory_total": gpu.memory_total,
                "memory_bandwidth": getattr(gpu, 'memory_bandwidth', None),
                "multiprocessor_count": getattr(gpu, 'multiprocessor_count', None),
                "max_threads_per_block": getattr(gpu, 'max_threads_per_block', None),
                "max_threads_per_multiprocessor": getattr(gpu, 'max_threads_per_multiprocessor', None)
            })
        
        return {
            "success": True,
            "data": capabilities
        }
    except Exception as e:
        logger.error(f"获取GPU能力信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取GPU能力信息失败: {str(e)}")


@router.get("/health", summary="GPU健康检查")
async def gpu_health_check(detector=Depends(get_gpu_detector)) -> Dict[str, Any]:
    """检查GPU健康状态"""
    try:
        gpu_info = await detector.get_all_gpu_info()

        health_status = []
        overall_healthy = True

        for gpu in gpu_info:
            # 简单的健康检查逻辑
            is_healthy = (
                gpu.temperature < 85 and  # 温度不超过85°C
                gpu.memory_total > 0 and  # 内存可用
                gpu.utilization >= 0      # 利用率正常
            )
            
            if not is_healthy:
                overall_healthy = False
            
            health_status.append({
                "gpu_id": gpu.gpu_id,
                "name": gpu.name,
                "healthy": is_healthy,
                "temperature": gpu.temperature,
                "memory_available": gpu.memory_free > 0,
                "issues": []
            })
            
            # 添加具体问题
            if gpu.temperature >= 85:
                health_status[-1]["issues"].append("温度过高")
            if gpu.memory_free <= 0:
                health_status[-1]["issues"].append("内存不足")
        
        return {
            "success": True,
            "data": {
                "overall_healthy": overall_healthy,
                "gpu_health": health_status,
                "total_gpus": len(gpu_info),
                "healthy_gpus": sum(1 for status in health_status if status["healthy"])
            }
        }
    except Exception as e:
        logger.error(f"GPU健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"GPU健康检查失败: {str(e)}")


@router.get("/statistics", summary="获取GPU统计信息")
async def get_gpu_statistics(detector=Depends(get_gpu_detector)) -> Dict[str, Any]:
    """获取GPU使用统计信息"""
    try:
        stats = await detector.to_dict()
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取GPU统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取GPU统计信息失败: {str(e)}")
