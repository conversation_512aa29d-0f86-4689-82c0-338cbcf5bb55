"""
vGPU 管理 API 路由
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import logging

from core.vgpu_manager import VGPUResource

logger = logging.getLogger(__name__)

router = APIRouter()


class VGPUAllocationRequest(BaseModel):
    """vGPU分配请求"""
    name: str
    memory_mb: int
    compute_percent: float
    owner: str
    physical_gpu_id: Optional[int] = None


class VGPUUpdateRequest(BaseModel):
    """vGPU更新请求"""
    name: Optional[str] = None
    memory_mb: Optional[int] = None
    compute_percent: Optional[float] = None


def get_vgpu_manager():
    """获取vGPU管理器实例"""
    from main import vgpu_manager
    if not vgpu_manager:
        raise HTTPException(status_code=503, detail="vGPU管理器未初始化")
    return vgpu_manager


@router.get("/list", summary="获取vGPU列表")
async def list_vgpus(manager=Depends(get_vgpu_manager)) -> Dict[str, Any]:
    """获取所有vGPU实例列表"""
    try:
        vgpus = await manager.list_vgpus()
        
        vgpu_list = []
        for vgpu in vgpus:
            vgpu_dict = vgpu.to_dict()
            vgpu_list.append(vgpu_dict)
        
        return {
            "success": True,
            "data": {
                "vgpus": vgpu_list,
                "total": len(vgpu_list)
            }
        }
    except Exception as e:
        logger.error(f"获取vGPU列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取vGPU列表失败: {str(e)}")


@router.post("/allocate", summary="分配vGPU")
async def allocate_vgpu(
    request: VGPUAllocationRequest,
    manager=Depends(get_vgpu_manager)
) -> Dict[str, Any]:
    """分配新的vGPU实例"""
    try:
        resource = VGPUResource(
            memory_mb=request.memory_mb,
            compute_percent=request.compute_percent
        )
        
        vgpu_id = await manager.allocate_vgpu(
            name=request.name,
            resource=resource,
            owner=request.owner,
            physical_gpu_id=request.physical_gpu_id
        )
        
        return {
            "success": True,
            "data": {
                "vgpu_id": vgpu_id,
                "message": f"vGPU {request.name} 分配成功"
            }
        }
    except Exception as e:
        logger.error(f"分配vGPU失败: {e}")
        raise HTTPException(status_code=500, detail=f"分配vGPU失败: {str(e)}")


@router.delete("/deallocate/{vgpu_id}", summary="释放vGPU")
async def deallocate_vgpu(
    vgpu_id: str,
    manager=Depends(get_vgpu_manager)
) -> Dict[str, Any]:
    """释放指定的vGPU实例"""
    try:
        success = await manager.deallocate_vgpu(vgpu_id)
        
        if success:
            return {
                "success": True,
                "message": f"vGPU {vgpu_id} 释放成功"
            }
        else:
            raise HTTPException(status_code=404, detail="vGPU实例不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"释放vGPU失败: {e}")
        raise HTTPException(status_code=500, detail=f"释放vGPU失败: {str(e)}")


@router.get("/info/{vgpu_id}", summary="获取vGPU信息")
async def get_vgpu_info(
    vgpu_id: str,
    manager=Depends(get_vgpu_manager)
) -> Dict[str, Any]:
    """获取指定vGPU的详细信息"""
    try:
        vgpu = await manager.get_vgpu_info(vgpu_id)
        
        if not vgpu:
            raise HTTPException(status_code=404, detail="vGPU实例不存在")
        
        return {
            "success": True,
            "data": vgpu.to_dict()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取vGPU信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取vGPU信息失败: {str(e)}")


@router.put("/update/{vgpu_id}", summary="更新vGPU配置")
async def update_vgpu(
    vgpu_id: str,
    request: VGPUUpdateRequest,
    manager=Depends(get_vgpu_manager)
) -> Dict[str, Any]:
    """更新vGPU配置"""
    try:
        # 过滤掉None值
        update_data = {k: v for k, v in request.dict().items() if v is not None}
        
        if not update_data:
            raise HTTPException(status_code=400, detail="没有提供更新数据")
        
        success = await manager.update_vgpu(vgpu_id, **update_data)
        
        if success:
            return {
                "success": True,
                "message": f"vGPU {vgpu_id} 配置更新成功"
            }
        else:
            raise HTTPException(status_code=404, detail="vGPU实例不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新vGPU配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新vGPU配置失败: {str(e)}")


@router.get("/capacity", summary="获取GPU容量信息")
async def get_gpu_capacity(manager=Depends(get_vgpu_manager)) -> Dict[str, Any]:
    """获取所有GPU的容量和使用情况"""
    try:
        capacity_info = []
        
        # 获取GPU信息
        gpu_info = await manager.gpu_detector.get_all_gpu_info()
        
        for gpu in gpu_info:
            # 计算该GPU上的vGPU使用情况
            gpu_vgpus = [v for v in manager.vgpu_instances.values() 
                        if v.physical_gpu_id == gpu.gpu_id]
            
            used_memory = sum(v.resource.memory_mb for v in gpu_vgpus)
            used_compute = sum(v.resource.compute_percent for v in gpu_vgpus)
            
            capacity_info.append({
                "gpu_id": gpu.gpu_id,
                "gpu_name": gpu.name,
                "total_memory": gpu.memory_total,
                "used_memory": used_memory,
                "available_memory": gpu.memory_total - used_memory,
                "used_compute": used_compute,
                "available_compute": max(0, 100 - used_compute),
                "vgpu_count": len(gpu_vgpus),
                "vgpus": [v.vgpu_id for v in gpu_vgpus]
            })
        
        return {
            "success": True,
            "data": {
                "gpu_capacity": capacity_info,
                "total_gpus": len(capacity_info)
            }
        }
    except Exception as e:
        logger.error(f"获取GPU容量信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取GPU容量信息失败: {str(e)}")


@router.get("/usage", summary="获取vGPU使用统计")
async def get_vgpu_usage(manager=Depends(get_vgpu_manager)) -> Dict[str, Any]:
    """获取vGPU使用统计信息"""
    try:
        usage_stats = await manager.get_usage_statistics()
        
        return {
            "success": True,
            "data": usage_stats
        }
    except Exception as e:
        logger.error(f"获取vGPU使用统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取vGPU使用统计失败: {str(e)}")


@router.get("/monitoring", summary="获取vGPU监控状态")
async def get_vgpu_monitoring_status(manager=Depends(get_vgpu_manager)) -> Dict[str, Any]:
    """获取vGPU监控状态"""
    try:
        return {
            "success": True,
            "data": {
                "is_monitoring": manager.is_monitoring(),
                "monitor_interval": getattr(manager, 'monitor_interval', None),
                "total_vgpus": len(manager.vgpu_instances),
                "active_vgpus": len([v for v in manager.vgpu_instances.values() 
                                   if v.status.value == "running"])
            }
        }
    except Exception as e:
        logger.error(f"获取vGPU监控状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取vGPU监控状态失败: {str(e)}")


@router.post("/monitoring/start", summary="开始vGPU监控")
async def start_vgpu_monitoring(manager=Depends(get_vgpu_manager)) -> Dict[str, Any]:
    """开始vGPU监控"""
    try:
        await manager.start_monitoring()
        return {
            "success": True,
            "message": "vGPU监控已开始"
        }
    except Exception as e:
        logger.error(f"开始vGPU监控失败: {e}")
        raise HTTPException(status_code=500, detail=f"开始vGPU监控失败: {str(e)}")


@router.post("/monitoring/stop", summary="停止vGPU监控")
async def stop_vgpu_monitoring(manager=Depends(get_vgpu_manager)) -> Dict[str, Any]:
    """停止vGPU监控"""
    try:
        await manager.stop_monitoring()
        return {
            "success": True,
            "message": "vGPU监控已停止"
        }
    except Exception as e:
        logger.error(f"停止vGPU监控失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止vGPU监控失败: {str(e)}")


@router.get("/health", summary="vGPU健康检查")
async def vgpu_health_check(manager=Depends(get_vgpu_manager)) -> Dict[str, Any]:
    """检查vGPU系统健康状态"""
    try:
        health_info = {
            "overall_healthy": True,
            "issues": [],
            "vgpu_count": len(manager.vgpu_instances),
            "gpu_utilization": []
        }
        
        # 检查GPU利用率
        gpu_info = await manager.gpu_detector.get_all_gpu_info()
        for gpu in gpu_info:
            gpu_vgpus = [v for v in manager.vgpu_instances.values() 
                        if v.physical_gpu_id == gpu.gpu_id]
            
            used_memory = sum(v.resource.memory_mb for v in gpu_vgpus)
            used_compute = sum(v.resource.compute_percent for v in gpu_vgpus)
            
            utilization = {
                "gpu_id": gpu.gpu_id,
                "memory_utilization": (used_memory / gpu.memory_total * 100) if gpu.memory_total > 0 else 0,
                "compute_utilization": used_compute,
                "vgpu_count": len(gpu_vgpus)
            }
            
            # 检查过度分配
            if used_compute > 100:
                health_info["overall_healthy"] = False
                health_info["issues"].append(f"GPU {gpu.gpu_id} 计算资源过度分配")
            
            if used_memory > gpu.memory_total:
                health_info["overall_healthy"] = False
                health_info["issues"].append(f"GPU {gpu.gpu_id} 内存过度分配")
            
            health_info["gpu_utilization"].append(utilization)
        
        return {
            "success": True,
            "data": health_info
        }
    except Exception as e:
        logger.error(f"vGPU健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"vGPU健康检查失败: {str(e)}")


@router.get("/statistics", summary="获取vGPU统计信息")
async def get_vgpu_statistics(manager=Depends(get_vgpu_manager)) -> Dict[str, Any]:
    """获取vGPU详细统计信息"""
    try:
        stats = await manager.to_dict()
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取vGPU统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取vGPU统计信息失败: {str(e)}")
