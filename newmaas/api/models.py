"""
模型管理 API 路由
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import logging

from core.model_manager import ModelType, ModelFramework

logger = logging.getLogger(__name__)

router = APIRouter()


class ModelRegistrationRequest(BaseModel):
    """模型注册请求"""
    name: str
    model_type: ModelType
    framework: ModelFramework
    model_path: str
    version: str = "1.0.0"
    memory_requirement: int = 1024
    compute_requirement: float = 25.0
    max_batch_size: int = 1
    max_sequence_length: int = 2048
    precision: str = "fp16"
    quantization: Optional[str] = None
    config_path: Optional[str] = None
    tokenizer_path: Optional[str] = None
    requirements: List[str] = []
    custom_params: Dict[str, Any] = {}
    tags: Dict[str, str] = {}
    description: str = ""
    api_enabled: bool = False
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    pricing: Dict[str, float] = {}


class ModelLoadRequest(BaseModel):
    """模型加载请求"""
    model_id: str
    gpu_id: Optional[int] = None
    custom_params: Dict[str, Any] = {}


class ModelUpdateRequest(BaseModel):
    """模型更新请求"""
    name: Optional[str] = None
    description: Optional[str] = None
    api_enabled: Optional[bool] = None
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    pricing: Optional[Dict[str, float]] = None
    tags: Optional[Dict[str, str]] = None


def get_model_manager():
    """获取模型管理器实例"""
    from main import model_manager
    if not model_manager:
        raise HTTPException(status_code=503, detail="模型管理器未初始化")
    return model_manager


@router.get("/list", summary="获取模型列表")
async def list_models(manager=Depends(get_model_manager)) -> Dict[str, Any]:
    """获取所有已注册的模型列表"""
    try:
        models = await manager.list_models()
        
        model_list = []
        for model in models:
            model_dict = model.to_dict()
            # 添加状态信息
            model_dict['status'] = model.get_status(manager.model_instances)
            model_list.append(model_dict)
        
        return {
            "success": True,
            "data": {
                "models": model_list,
                "total": len(model_list)
            }
        }
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@router.post("/register", summary="注册新模型")
async def register_model(
    request: ModelRegistrationRequest,
    manager=Depends(get_model_manager)
) -> Dict[str, Any]:
    """注册新的模型配置"""
    try:
        model_id = await manager.register_model(
            name=request.name,
            model_type=request.model_type,
            framework=request.framework,
            model_path=request.model_path,
            version=request.version,
            memory_requirement=request.memory_requirement,
            compute_requirement=request.compute_requirement,
            max_batch_size=request.max_batch_size,
            max_sequence_length=request.max_sequence_length,
            precision=request.precision,
            quantization=request.quantization,
            config_path=request.config_path,
            tokenizer_path=request.tokenizer_path,
            requirements=request.requirements,
            custom_params=request.custom_params,
            tags=request.tags,
            description=request.description
        )
        
        # 如果启用API，更新API配置
        if request.api_enabled:
            await manager.update_model(
                model_id,
                api_enabled=request.api_enabled,
                api_key=request.api_key,
                base_url=request.base_url,
                pricing=request.pricing
            )
        
        return {
            "success": True,
            "data": {
                "model_id": model_id,
                "message": f"模型 {request.name} 注册成功"
            }
        }
    except Exception as e:
        logger.error(f"注册模型失败: {e}")
        raise HTTPException(status_code=500, detail=f"注册模型失败: {str(e)}")


@router.post("/load", summary="加载模型")
async def load_model(
    request: ModelLoadRequest,
    manager=Depends(get_model_manager)
) -> Dict[str, Any]:
    """加载指定模型到GPU"""
    try:
        instance_id = await manager.load_model(
            request.model_id,
            gpu_id=request.gpu_id,
            custom_params=request.custom_params
        )
        
        return {
            "success": True,
            "data": {
                "instance_id": instance_id,
                "message": f"模型加载成功，实例ID: {instance_id}"
            }
        }
    except Exception as e:
        logger.error(f"加载模型失败: {e}")
        raise HTTPException(status_code=500, detail=f"加载模型失败: {str(e)}")


@router.post("/unload/{instance_id}", summary="卸载模型")
async def unload_model(
    instance_id: str,
    manager=Depends(get_model_manager)
) -> Dict[str, Any]:
    """卸载指定的模型实例"""
    try:
        success = await manager.unload_model(instance_id)
        
        if success:
            return {
                "success": True,
                "message": f"模型实例 {instance_id} 卸载成功"
            }
        else:
            raise HTTPException(status_code=404, detail="模型实例不存在")
    except Exception as e:
        logger.error(f"卸载模型失败: {e}")
        raise HTTPException(status_code=500, detail=f"卸载模型失败: {str(e)}")


@router.get("/instances", summary="获取模型实例列表")
async def list_model_instances(manager=Depends(get_model_manager)) -> Dict[str, Any]:
    """获取所有模型实例的状态"""
    try:
        instances = []
        for instance_id, instance in manager.model_instances.items():
            instances.append(instance.to_dict())
        
        return {
            "success": True,
            "data": {
                "instances": instances,
                "total": len(instances)
            }
        }
    except Exception as e:
        logger.error(f"获取模型实例列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型实例列表失败: {str(e)}")


@router.get("/config/{model_id}", summary="获取模型配置")
async def get_model_config(
    model_id: str,
    manager=Depends(get_model_manager)
) -> Dict[str, Any]:
    """获取指定模型的详细配置"""
    try:
        config = await manager.get_model_config(model_id)
        
        if not config:
            raise HTTPException(status_code=404, detail="模型不存在")
        
        config_dict = config.to_dict()
        config_dict['status'] = config.get_status(manager.model_instances)
        
        return {
            "success": True,
            "data": config_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型配置失败: {str(e)}")


@router.put("/config/{model_id}", summary="更新模型配置")
async def update_model_config(
    model_id: str,
    request: ModelUpdateRequest,
    manager=Depends(get_model_manager)
) -> Dict[str, Any]:
    """更新模型配置"""
    try:
        # 过滤掉None值
        update_data = {k: v for k, v in request.dict().items() if v is not None}
        
        if not update_data:
            raise HTTPException(status_code=400, detail="没有提供更新数据")
        
        success = await manager.update_model(model_id, **update_data)
        
        if success:
            return {
                "success": True,
                "message": f"模型 {model_id} 配置更新成功"
            }
        else:
            raise HTTPException(status_code=404, detail="模型不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新模型配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新模型配置失败: {str(e)}")


@router.delete("/config/{model_id}", summary="删除模型")
async def delete_model(
    model_id: str,
    manager=Depends(get_model_manager)
) -> Dict[str, Any]:
    """删除指定模型"""
    try:
        success = await manager.delete_model(model_id)
        
        if success:
            return {
                "success": True,
                "message": f"模型 {model_id} 删除成功"
            }
        else:
            raise HTTPException(status_code=404, detail="模型不存在")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除模型失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除模型失败: {str(e)}")


@router.get("/frameworks", summary="获取支持的框架列表")
async def get_supported_frameworks() -> Dict[str, Any]:
    """获取支持的模型框架列表"""
    try:
        frameworks = [
            {
                "name": framework.value,
                "display_name": framework.name,
                "description": f"{framework.value} 框架支持"
            }
            for framework in ModelFramework
        ]
        
        return {
            "success": True,
            "data": frameworks
        }
    except Exception as e:
        logger.error(f"获取框架列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取框架列表失败: {str(e)}")


@router.get("/types", summary="获取支持的模型类型")
async def get_supported_model_types() -> Dict[str, Any]:
    """获取支持的模型类型列表"""
    try:
        model_types = [
            {
                "name": model_type.value,
                "display_name": model_type.name,
                "description": f"{model_type.value} 类型模型"
            }
            for model_type in ModelType
        ]
        
        return {
            "success": True,
            "data": model_types
        }
    except Exception as e:
        logger.error(f"获取模型类型列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型类型列表失败: {str(e)}")


@router.get("/statistics", summary="获取模型统计信息")
async def get_model_statistics(manager=Depends(get_model_manager)) -> Dict[str, Any]:
    """获取模型使用统计信息"""
    try:
        stats = await manager.to_dict()
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取模型统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型统计信息失败: {str(e)}")
