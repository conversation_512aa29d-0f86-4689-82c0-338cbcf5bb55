<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}NewMaaS - 多模型管理平台{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- 自定义样式 -->
    <link href="/static/css/style.css" rel="stylesheet">

    <!-- 禁用旋转动画 -->
    <style>
        /* 全局禁用所有旋转动画 */
        .spinner-border,
        .spinner-border-sm,
        .spinner-grow,
        .spinner-grow-sm {
            animation: none !important;
            -webkit-animation: none !important;
            -moz-animation: none !important;
            -o-animation: none !important;
            -ms-animation: none !important;
        }

        /* 禁用Bootstrap的旋转关键帧 */
        @keyframes spinner-border {
            to { transform: rotate(0deg) !important; }
        }

        @-webkit-keyframes spinner-border {
            to { -webkit-transform: rotate(0deg) !important; }
        }

        @keyframes spinner-grow {
            0%, 100% { transform: scale(0) !important; opacity: 1 !important; }
            50% { transform: scale(0) !important; opacity: 1 !important; }
        }
    </style>

    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-microchip me-2"></i>
                NewMaaS 多模型管理平台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/maas">
                            <i class="fas fa-cloud me-1"></i>
                            MaaS 服务
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/model-library">
                            <i class="fas fa-database me-1"></i>
                            模型库
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>
                            设置
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/docs" target="_blank">
                                <i class="fas fa-book me-1"></i>
                                API 文档
                            </a></li>
                            <li><a class="dropdown-item" href="/health" target="_blank">
                                <i class="fas fa-heartbeat me-1"></i>
                                健康检查
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 col-lg-2">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-list me-1"></i>
                            功能菜单
                        </h6>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="#gpu-section" class="list-group-item list-group-item-action" data-section="gpu">
                            <i class="fas fa-microchip me-2"></i>
                            GPU 管理
                        </a>
                        <a href="#vgpu-section" class="list-group-item list-group-item-action" data-section="vgpu">
                            <i class="fas fa-layer-group me-2"></i>
                            vGPU 管理
                        </a>
                        <a href="#model-section" class="list-group-item list-group-item-action" data-section="models">
                            <i class="fas fa-brain me-2"></i>
                            模型管理
                        </a>
                        <a href="#system-section" class="list-group-item list-group-item-action" data-section="system">
                            <i class="fas fa-server me-2"></i>
                            系统信息
                        </a>
                    </div>
                </div>
                
                <!-- 系统状态卡片 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-heartbeat me-1"></i>
                            系统状态
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="system-status">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>GPU 监控</span>
                                <span class="badge bg-success" id="gpu-status">运行中</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>模型服务</span>
                                <span class="badge bg-success" id="model-status">运行中</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>vGPU 服务</span>
                                <span class="badge bg-success" id="vgpu-status">运行中</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span>MaaS 服务</span>
                                <span class="badge bg-success" id="maas-status">运行中</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区域 -->
            <div class="col-md-9 col-lg-10">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;">
    </div>

    <!-- 加载遮罩 -->
    <div id="loading-overlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" style="background: rgba(0,0,0,0.5); z-index: 9999;">
        <div class="d-flex justify-content-center align-items-center h-100">
            <div class="text-light text-center">
                <div style="font-size: 3rem;">
                    <i class="fas fa-microchip"></i>
                </div>
                <div class="mt-2">加载中...</div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义 JavaScript -->
    <script src="/static/js/common.js"></script>
    
    {% block extra_scripts %}{% endblock %}
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化通用功能
            initializeCommon();
            
            // 加载系统状态
            loadSystemStatus();
            
            // 设置定时刷新
            setInterval(loadSystemStatus, 30000); // 30秒刷新一次
            
            // 初始化页面特定功能
            if (typeof initializePage === 'function') {
                initializePage();
            }
        });
        
        // 加载系统状态
        async function loadSystemStatus() {
            try {
                const response = await fetch('/api/system/status');
                const data = await response.json();
                
                if (data.success) {
                    updateSystemStatus(data.data);
                }
            } catch (error) {
                console.error('加载系统状态失败:', error);
            }
        }
        
        // 更新系统状态显示
        function updateSystemStatus(status) {
            const services = status.services || {};
            
            updateStatusBadge('gpu-status', services.gpu_monitoring);
            updateStatusBadge('model-status', services.model_monitoring);
            updateStatusBadge('vgpu-status', services.vgpu_monitoring);
            updateStatusBadge('maas-status', services.maas_service);
        }
        
        // 更新状态徽章
        function updateStatusBadge(elementId, isActive) {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = `badge ${isActive ? 'bg-success' : 'bg-danger'}`;
                element.textContent = isActive ? '运行中' : '已停止';
            }
        }
        
        // 侧边栏导航
        document.querySelectorAll('[data-section]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const section = this.dataset.section;
                
                // 移除所有活动状态
                document.querySelectorAll('[data-section]').forEach(l => {
                    l.classList.remove('active');
                });
                
                // 添加当前活动状态
                this.classList.add('active');
                
                // 触发自定义事件
                document.dispatchEvent(new CustomEvent('sectionChange', {
                    detail: { section: section }
                }));
            });
        });
    </script>
</body>
</html>
