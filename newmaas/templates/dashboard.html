{% extends "base.html" %}

{% block title %}仪表板 - NewMaaS{% endblock %}

{% block content %}
<!-- 概览卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="gpu-count">0</h4>
                        <p class="card-text">GPU 总数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-microchip fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="model-count">0</h4>
                        <p class="card-text">已注册模型</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-brain fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="vgpu-count">0</h4>
                        <p class="card-text">vGPU 实例</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-layer-group fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="api-key-count">0</h4>
                        <p class="card-text">API 密钥</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-key fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- GPU 管理部分 -->
<div id="gpu-section" class="section-content">
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-microchip me-2"></i>
                GPU 管理
            </h5>
            <button class="btn btn-outline-primary btn-sm" onclick="refreshGPUInfo()">
                <i class="fas fa-sync-alt me-1"></i>
                刷新
            </button>
        </div>
        <div class="card-body">
            <div id="gpu-info-container">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模型管理部分 -->
<div id="model-section" class="section-content d-none">
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-brain me-2"></i>
                模型管理
            </h5>
            <div>
                <button class="btn btn-primary btn-sm me-2" onclick="showModelRegistrationModal()">
                    <i class="fas fa-plus me-1"></i>
                    注册模型
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshModelList()">
                    <i class="fas fa-sync-alt me-1"></i>
                    刷新
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="model-list-container">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- vGPU 管理部分 -->
<div id="vgpu-section" class="section-content d-none">
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-layer-group me-2"></i>
                vGPU 管理
            </h5>
            <div>
                <button class="btn btn-primary btn-sm me-2" onclick="showVGPUAllocationModal()">
                    <i class="fas fa-plus me-1"></i>
                    分配 vGPU
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshVGPUList()">
                    <i class="fas fa-sync-alt me-1"></i>
                    刷新
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="vgpu-list-container">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息部分 -->
<div id="system-section" class="section-content d-none">
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-server me-2"></i>
                系统信息
            </h5>
            <button class="btn btn-outline-primary btn-sm" onclick="refreshSystemInfo()">
                <i class="fas fa-sync-alt me-1"></i>
                刷新
            </button>
        </div>
        <div class="card-body">
            <div id="system-info-container">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模型注册模态框 -->
<div class="modal fade" id="modelRegistrationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">注册新模型</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="modelRegistrationForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">模型名称</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">模型类型</label>
                                <select class="form-select" name="model_type" required>
                                    <option value="">请选择</option>
                                    <option value="llm">大语言模型</option>
                                    <option value="vision">视觉模型</option>
                                    <option value="multimodal">多模态模型</option>
                                    <option value="embedding">嵌入模型</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">框架</label>
                                <select class="form-select" name="framework" required>
                                    <option value="">请选择</option>
                                    <option value="transformers">Transformers</option>
                                    <option value="vllm">vLLM</option>
                                    <option value="llamacpp">llama.cpp</option>
                                    <option value="ollama">Ollama</option>
                                    <option value="tensorrt">TensorRT</option>
                                    <option value="onnx">ONNX</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">版本</label>
                                <input type="text" class="form-control" name="version" value="1.0.0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">模型路径</label>
                        <input type="text" class="form-control" name="model_path" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">内存需求 (MB)</label>
                                <input type="number" class="form-control" name="memory_requirement" value="1024">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">计算需求 (%)</label>
                                <input type="number" class="form-control" name="compute_requirement" value="25" min="1" max="100">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="api_enabled" id="apiEnabled">
                        <label class="form-check-label" for="apiEnabled">
                            启用 API 访问
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitModelRegistration()">注册模型</button>
            </div>
        </div>
    </div>
</div>

<!-- vGPU 分配模态框 -->
<div class="modal fade" id="vgpuAllocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">分配 vGPU</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="vgpuAllocationForm">
                    <div class="mb-3">
                        <label class="form-label">vGPU 名称</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">内存大小 (MB)</label>
                        <input type="number" class="form-control" name="memory_mb" required min="512">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">计算资源 (%)</label>
                        <input type="number" class="form-control" name="compute_percent" required min="1" max="100">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">所有者</label>
                        <input type="text" class="form-control" name="owner" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">物理 GPU (可选)</label>
                        <select class="form-select" name="physical_gpu_id">
                            <option value="">自动选择</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitVGPUAllocation()">分配 vGPU</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="/static/js/dashboard.js"></script>
{% endblock %}
