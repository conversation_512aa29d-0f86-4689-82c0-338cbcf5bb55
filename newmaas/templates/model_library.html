{% extends "base.html" %}

{% block title %}模型库管理{% endblock %}

{% block extra_css %}
<style>
.model-card {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    background: white;
}

.model-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.model-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.model-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.model-provider {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin: 0;
}

.model-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-available { background: #d4edda; color: #155724; }
.status-beta { background: #fff3cd; color: #856404; }
.status-unavailable { background: #f8d7da; color: #721c24; }

.model-description {
    color: #555;
    margin-bottom: 15px;
    line-height: 1.5;
}

.model-tags {
    margin-bottom: 15px;
}

.model-tag {
    display: inline-block;
    background: #f8f9fa;
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-right: 8px;
    margin-bottom: 4px;
}

.model-specs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.spec-label {
    font-weight: 500;
    color: #495057;
}

.spec-value {
    color: #007bff;
    font-weight: 600;
}

.model-pricing {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.pricing-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.pricing-row:last-child {
    margin-bottom: 0;
}

.filter-sidebar {
    background: white;
    border-radius: 12px;
    padding: 20px;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.filter-section {
    margin-bottom: 25px;
}

.filter-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 12px;
    font-size: 1rem;
}

.filter-group {
    margin-bottom: 15px;
}

.search-box {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 0.9rem;
}

.sort-controls {
    background: white;
    padding: 15px 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-info {
    color: #6c757d;
    font-size: 0.9rem;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.loading-spinner {
    text-align: center;
    padding: 40px;
}

.loading-spinner .spinner-border {
    animation: none !important;
}

.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.capabilities-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.capability-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.model-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .model-specs {
        grid-template-columns: 1fr;
    }
    
    .sort-controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .model-header {
        flex-direction: column;
        gap: 10px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- 筛选侧边栏 -->
        <div class="col-lg-3 col-md-4">
            <div class="filter-sidebar">
                <h5 class="mb-3">
                    <i class="fas fa-filter me-2"></i>筛选条件
                </h5>
                
                <!-- 搜索框 -->
                <div class="filter-section">
                    <div class="filter-title">搜索</div>
                    <input type="text" id="searchInput" class="search-box" placeholder="搜索模型名称、描述...">
                </div>
                
                <!-- 模型系列 -->
                <div class="filter-section">
                    <div class="filter-title">模型系列</div>
                    <div id="seriesFilters"></div>
                </div>
                
                <!-- 模型分类 -->
                <div class="filter-section">
                    <div class="filter-title">适用分类</div>
                    <div id="categoryFilters"></div>
                </div>
                
                <!-- 输入模态 -->
                <div class="filter-section">
                    <div class="filter-title">输入模态</div>
                    <div id="modalityFilters"></div>
                </div>
                
                <!-- 模型状态 -->
                <div class="filter-section">
                    <div class="filter-title">模型状态</div>
                    <div id="statusFilters"></div>
                </div>
                
                <!-- 上下文长度 -->
                <div class="filter-section">
                    <div class="filter-title">上下文长度</div>
                    <div class="filter-group">
                        <label class="form-label">最小值</label>
                        <input type="number" id="minContextLength" class="form-control form-control-sm" placeholder="如: 4000">
                    </div>
                    <div class="filter-group">
                        <label class="form-label">最大值</label>
                        <input type="number" id="maxContextLength" class="form-control form-control-sm" placeholder="如: 128000">
                    </div>
                </div>
                
                <!-- 价格范围 -->
                <div class="filter-section">
                    <div class="filter-title">价格范围 (¥/1K tokens)</div>
                    <div class="filter-group">
                        <label class="form-label">最低价格</label>
                        <input type="number" id="minPrice" class="form-control form-control-sm" step="0.001" placeholder="如: 0.001">
                    </div>
                    <div class="filter-group">
                        <label class="form-label">最高价格</label>
                        <input type="number" id="maxPrice" class="form-control form-control-sm" step="0.001" placeholder="如: 1.0">
                    </div>
                </div>
                
                <!-- 能力筛选 -->
                <div class="filter-section">
                    <div class="filter-title">模型能力</div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="supportsTools">
                        <label class="form-check-label" for="supportsTools">支持工具调用</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="supportsVision">
                        <label class="form-check-label" for="supportsVision">支持视觉</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="supportsStreaming">
                        <label class="form-check-label" for="supportsStreaming">支持流式输出</label>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary" onclick="applyFilters()">
                        <i class="fas fa-search me-2"></i>应用筛选
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                        <i class="fas fa-undo me-2"></i>重置筛选
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="col-lg-9 col-md-8">
            <!-- 排序控制 -->
            <div class="sort-controls">
                <div class="results-info">
                    <span id="resultsCount">加载中...</span>
                </div>
                <div class="d-flex gap-3 align-items-center">
                    <div class="d-flex align-items-center gap-2">
                        <label class="form-label mb-0">排序:</label>
                        <select id="sortBy" class="form-select form-select-sm" style="width: auto;">
                            <option value="name">名称</option>
                            <option value="price">价格</option>
                            <option value="context_length">上下文长度</option>
                            <option value="created_at">创建时间</option>
                            <option value="updated_at">更新时间</option>
                        </select>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <select id="sortOrder" class="form-select form-select-sm" style="width: auto;">
                            <option value="asc">升序</option>
                            <option value="desc">降序</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-success btn-sm" onclick="showAddModelModal()">
                        <i class="fas fa-plus me-2"></i>添加模型
                    </button>
                </div>
            </div>
            
            <!-- 模型列表 -->
            <div id="modelsList">
                <div class="loading-spinner">
                    <div class="text-primary" style="font-size: 2rem;">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="mt-2">正在加载模型库...</div>
                </div>
            </div>
            
            <!-- 分页 -->
            <div class="pagination-wrapper">
                <nav aria-label="模型列表分页">
                    <ul class="pagination" id="pagination"></ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 添加模型模态框 -->
<div class="modal fade" id="addModelModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加新模型</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addModelForm">
                    <!-- 表单内容将通过JavaScript动态生成 -->
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitAddModel()">添加模型</button>
            </div>
        </div>
    </div>
</div>

<!-- 模型详情模态框 -->
<div class="modal fade" id="modelDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">模型详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modelDetailContent">
                <!-- 详情内容将通过JavaScript动态生成 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', path='js/model_library.js') }}"></script>
{% endblock %}
