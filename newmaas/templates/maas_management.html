{% extends "base.html" %}

{% block title %}MaaS 服务管理 - NewMaaS{% endblock %}

{% block content %}
<!-- MaaS 概览 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="total-api-keys">0</h4>
                        <p class="card-text">API 密钥总数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-key fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="active-api-keys">0</h4>
                        <p class="card-text">活跃密钥</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="total-requests">0</h4>
                        <p class="card-text">总请求数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title" id="total-cost">¥0.00</h4>
                        <p class="card-text">总费用</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-yen-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API 密钥管理 -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-key me-2"></i>
            API 密钥管理
        </h5>
        <div>
            <button class="btn btn-primary btn-sm me-2" onclick="showCreateAPIKeyModal()">
                <i class="fas fa-plus me-1"></i>
                创建密钥
            </button>
            <button class="btn btn-outline-primary btn-sm" onclick="refreshAPIKeys()">
                <i class="fas fa-sync-alt me-1"></i>
                刷新
            </button>
        </div>
    </div>
    <div class="card-body">
        <div id="api-keys-container">
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 使用统计 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-chart-bar me-2"></i>
            使用统计
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <canvas id="requestsChart" width="400" height="200"></canvas>
            </div>
            <div class="col-md-6">
                <canvas id="costChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- OpenAI 兼容 API 文档 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-book me-2"></i>
            OpenAI 兼容 API
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>基础 URL</h6>
                <div class="input-group mb-3">
                    <input type="text" class="form-control" id="base-url" readonly>
                    <button class="btn btn-outline-secondary" onclick="copyToClipboard('base-url')">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                
                <h6>模型列表</h6>
                <code>GET /v1/models</code>
                
                <h6 class="mt-3">聊天完成</h6>
                <code>POST /v1/chat/completions</code>
            </div>
            <div class="col-md-6">
                <h6>示例请求</h6>
                <pre><code id="api-example">curl -X POST "http://localhost:8000/v1/chat/completions" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "your-model-id",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ]
  }'</code></pre>
                <button class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('api-example')">
                    <i class="fas fa-copy me-1"></i>
                    复制示例
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 创建 API 密钥模态框 -->
<div class="modal fade" id="createAPIKeyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建 API 密钥</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createAPIKeyForm">
                    <div class="mb-3">
                        <label class="form-label">密钥名称</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">模型访问权限</label>
                        <div id="model-access-checkboxes">
                            <!-- 动态加载模型列表 -->
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">请求频率限制 (每分钟)</label>
                        <input type="number" class="form-control" name="rate_limit" value="1000" min="1">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">使用额度限制 (¥)</label>
                        <input type="number" class="form-control" name="usage_limit" step="0.01" placeholder="不限制">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">有效期 (天)</label>
                        <input type="number" class="form-control" name="expires_days" placeholder="永不过期">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitCreateAPIKey()">创建密钥</button>
            </div>
        </div>
    </div>
</div>

<!-- API 密钥详情模态框 -->
<div class="modal fade" id="apiKeyDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">API 密钥详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="api-key-details-content">
                    <!-- 动态加载内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 新创建的 API 密钥显示模态框 -->
<div class="modal fade" id="newAPIKeyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    API 密钥创建成功
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>重要提示：</strong>请立即复制并保存您的 API 密钥，密钥只会显示一次！
                </div>
                
                <div class="mb-3">
                    <label class="form-label">API 密钥</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="new-api-key" readonly>
                        <button class="btn btn-outline-secondary" onclick="copyToClipboard('new-api-key')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">密钥 ID</label>
                    <input type="text" class="form-control" id="new-key-id" readonly>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">我已保存密钥</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="/static/js/maas.js"></script>
{% endblock %}
