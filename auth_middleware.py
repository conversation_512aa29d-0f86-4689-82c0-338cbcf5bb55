import jwt
import hashlib
import secrets
from datetime import datetime, timedelta
from functools import wraps
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Role(Enum):
    ADMIN = "admin"
    USER = "user"
    VIEWER = "viewer"

@dataclass
class User:
    user_id: str
    username: str
    password_hash: str
    roles: List[Role]
    api_quota: int
    is_active: bool = True
    created_at: datetime = None
    last_login: datetime = None

class AuthenticationError(Exception):
    pass

class AuthorizationError(Exception):
    pass

class RateLimitExceededError(Exception):
    pass

class ModelAPIAuth:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.users: Dict[str, User] = {}
        self.user_sessions: Dict[str, Dict] = {}
        self.rate_limits: Dict[str, List[datetime]] = {}
        
    def hash_password(self, password: str, salt: str = None) -> tuple:
        if salt is None:
            salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return password_hash.hex(), salt
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        computed_hash, _ = self.hash_password(password, salt)
        return secrets.compare_digest(computed_hash, password_hash)
    
    def create_user(self, username: str, password: str, roles: List[Role], api_quota: int = 1000) -> str:
        user_id = secrets.token_urlsafe(16)
        password_hash, salt = self.hash_password(password)
        
        user = User(
            user_id=user_id,
            username=username,
            password_hash=f"{password_hash}:{salt}",
            roles=roles,
            api_quota=api_quota,
            created_at=datetime.utcnow()
        )
        
        self.users[user_id] = user
        logger.info(f"User created: {username} with roles: {[r.value for r in roles]}")
        return user_id
    
    def authenticate_user(self, username: str, password: str) -> Optional[str]:
        for user_id, user in self.users.items():
            if user.username == username and user.is_active:
                password_hash, salt = user.password_hash.split(':')
                if self.verify_password(password, password_hash, salt):
                    user.last_login = datetime.utcnow()
                    logger.info(f"User authenticated: {username}")
                    return user_id
        
        logger.warning(f"Authentication failed for user: {username}")
        return None
    
    def generate_token(self, user_id: str, expires_in_hours: int = 24) -> str:
        if user_id not in self.users:
            raise AuthenticationError("User not found")
        
        user = self.users[user_id]
        payload = {
            'user_id': user_id,
            'username': user.username,
            'roles': [role.value for role in user.roles],
            'exp': datetime.utcnow() + timedelta(hours=expires_in_hours),
            'iat': datetime.utcnow()
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm='HS256')
        self.user_sessions[user_id] = {
            'token': token,
            'expires_at': payload['exp'],
            'created_at': payload['iat']
        }
        
        logger.info(f"Token generated for user: {user.username}")
        return token
    
    def verify_token(self, token: str) -> Dict:
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            user_id = payload['user_id']
            
            if user_id not in self.users or not self.users[user_id].is_active:
                raise AuthenticationError("User not found or inactive")
            
            return payload
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token expired")
        except jwt.InvalidTokenError:
            raise AuthenticationError("Invalid token")
    
    def check_permissions(self, user_id: str, required_roles: List[Role]) -> bool:
        if user_id not in self.users:
            return False
        
        user = self.users[user_id]
        return any(role in user.roles for role in required_roles)
    
    def check_rate_limit(self, user_id: str, max_requests: int = 100, window_minutes: int = 60) -> bool:
        now = datetime.utcnow()
        window_start = now - timedelta(minutes=window_minutes)
        
        if user_id not in self.rate_limits:
            self.rate_limits[user_id] = []
        
        user_requests = self.rate_limits[user_id]
        user_requests[:] = [req_time for req_time in user_requests if req_time > window_start]
        
        if len(user_requests) >= max_requests:
            logger.warning(f"Rate limit exceeded for user: {user_id}")
            return False
        
        user_requests.append(now)
        return True
    
    def audit_log(self, user_id: str, action: str, resource: str, success: bool = True):
        user = self.users.get(user_id, {})
        username = getattr(user, 'username', 'unknown')
        
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'user_id': user_id,
            'username': username,
            'action': action,
            'resource': resource,
            'success': success,
            'ip_address': None  # Would be populated from request context
        }
        
        if success:
            logger.info(f"AUDIT: {username} performed {action} on {resource}")
        else:
            logger.warning(f"AUDIT: Failed {action} attempt by {username} on {resource}")

def require_auth(required_roles: List[Role] = None, check_quota: bool = True):
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            auth_header = kwargs.get('authorization') or getattr(args[0] if args else None, 'headers', {}).get('Authorization')
            
            if not auth_header or not auth_header.startswith('Bearer '):
                raise AuthenticationError("Missing or invalid authorization header")
            
            token = auth_header.split(' ')[1]
            auth_system = kwargs.get('auth_system')
            
            if not auth_system:
                raise AuthenticationError("Authentication system not configured")
            
            try:
                payload = auth_system.verify_token(token)
                user_id = payload['user_id']
                
                if required_roles and not auth_system.check_permissions(user_id, required_roles):
                    auth_system.audit_log(user_id, func.__name__, "access_denied", False)
                    raise AuthorizationError("Insufficient permissions")
                
                if check_quota and not auth_system.check_rate_limit(user_id):
                    auth_system.audit_log(user_id, func.__name__, "rate_limit_exceeded", False)
                    raise RateLimitExceededError("Rate limit exceeded")
                
                kwargs['current_user'] = payload
                result = func(*args, **kwargs)
                
                auth_system.audit_log(user_id, func.__name__, "success", True)
                return result
                
            except (AuthenticationError, AuthorizationError, RateLimitExceededError):
                raise
            except Exception as e:
                logger.error(f"Authentication error: {str(e)}")
                raise AuthenticationError("Authentication failed")
        
        return wrapper
    return decorator