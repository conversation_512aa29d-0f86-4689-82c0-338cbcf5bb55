# Model API Authentication & Access Control System

A secure authentication and authorization system for AI model inference APIs with JWT tokens, role-based access control, rate limiting, and audit logging.

## Features

- **JWT Authentication**: Secure token-based authentication
- **Role-Based Access Control (RBAC)**: Admin, User, and Viewer roles
- **Rate Limiting**: Configurable API quotas and request limits
- **Audit Logging**: Comprehensive access and action logging
- **User Management**: Full CRUD operations for user accounts
- **Password Security**: PBKDF2 hashing with salt

## Quick Start

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Run the server:
```bash
python user_management.py
```

Default admin credentials:
- Username: `admin`
- Password: `admin123`

## API Endpoints

### Authentication
- `POST /auth/login` - User login
- `GET /auth/profile` - Get current user profile

### User Management (Admin only)
- `POST /auth/users` - Create new user
- `GET /auth/users` - List all users
- `PUT /auth/users/<user_id>` - Update user
- `DELETE /auth/users/<user_id>` - Delete user

### Model Inference (Authenticated users)
- `POST /api/models/<model_name>/inference` - Run model inference

## Usage Examples

### Login
```bash
curl -X POST http://localhost:5000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### Create User (Admin)
```bash
curl -X POST http://localhost:5000/auth/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "username": "testuser",
    "password": "password123",
    "roles": ["user"],
    "api_quota": 1000
  }'
```

### Model Inference
```bash
curl -X POST http://localhost:5000/api/models/gpt-3.5/inference \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <user_token>" \
  -d '{"prompt": "Hello world"}'
```

## Security Features

- **Password Hashing**: PBKDF2 with 100,000 iterations
- **Token Expiration**: JWT tokens expire after 24 hours
- **Rate Limiting**: 100 requests per hour by default
- **Audit Logging**: All API access logged with timestamps
- **Role Separation**: Strict role-based permissions
- **Input Validation**: All inputs validated and sanitized