from langchain_openai import ChatOpenAI  # 更新导入路径
from langchain.agents import initialize_agent, AgentType
from langchain.tools import BaseTool
from browser_use import BrowserUse
from pydantic import SecretStr, BaseModel, Field
from typing import Optional, Type

# 定义搜索工具
class SearchTool(BaseTool):
    name = "web_search"
    description = "用于在网络上搜索信息的工具，输入搜索关键词"
    
    def _run(self, query: str) -> str:
        browser = BrowserUse(headless=False)
        result = browser.search(query)
        browser.quit()
        return result
    
    async def _arun(self, query: str) -> str:
        return self._run(query)

# 初始化LLM
llm = ChatOpenAI(
    base_url="https://api.deepseek.com/v1",
    model="deepseek-reasoner",
    api_key=SecretStr("api_key"),
    temperature=0
)

# 创建工具列表
tools = [SearchTool()]

# 初始化Agent
agent = initialize_agent(
    tools=tools,
    llm=llm,
    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
    verbose=True
)

# 执行搜索任务
def search_iphone_price():
    query = "iPhone 16 Pro Max 价格 多少钱"
    result = agent.run(f"请搜索并告诉我{query}的详细信息，包括不同配置的价格。")
    print(result)

if __name__ == "__main__":
    search_iphone_price()
