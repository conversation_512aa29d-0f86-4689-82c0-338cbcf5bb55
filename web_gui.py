from flask import Flask, render_template, request, jsonify, session
import threading
import time
from langchain_openai import ChatOpenAI
from langchain.agents import initialize_agent, AgentType
from langchain.tools import BaseTool
from browser_use import Browser
from pydantic import SecretStr, BaseModel, Field
from typing import Optional, Type
import secrets
import uuid

app = Flask(__name__)
app.secret_key = secrets.token_hex(16)

class SearchTool(BaseTool):
    name: str = "web_search"
    description: str = "用于在网络上搜索信息的工具，输入搜索关键词"
    
    def _run(self, query: str) -> str:
        browser = Browser()
        result = browser.search(query)
        browser.close()
        return result
    
    async def _arun(self, query: str) -> str:
        return self._run(query)

# 全局变量存储搜索状态
search_sessions = {}

class SearchSession:
    def __init__(self):
        self.llm = None
        self.agent = None
        self.is_searching = False
        self.result = ""
        self.error = ""
        self.progress = 0

@app.route('/')
def index():
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
    
    session_id = session['session_id']
    if session_id not in search_sessions:
        search_sessions[session_id] = SearchSession()
    
    return render_template('index.html')

@app.route('/initialize', methods=['POST'])
def initialize_ai():
    session_id = session['session_id']
    search_session = search_sessions[session_id]
    
    data = request.json
    api_key = data.get('api_key', '').strip()
    base_url = data.get('base_url', '').strip()
    
    if not api_key or not base_url:
        return jsonify({'success': False, 'message': '请填写完整的API配置信息'})
    
    try:
        search_session.llm = ChatOpenAI(
            base_url=base_url,
            model="deepseek-reasoner",
            api_key=SecretStr(api_key),
            temperature=0
        )
        
        tools = [SearchTool()]
        search_session.agent = initialize_agent(
            tools=tools,
            llm=search_session.llm,
            agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
            verbose=True
        )
        
        return jsonify({'success': True, 'message': 'AI初始化成功'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'AI初始化失败: {str(e)}'})

@app.route('/search', methods=['POST'])
def start_search():
    session_id = session['session_id']
    search_session = search_sessions[session_id]
    
    if not search_session.agent:
        return jsonify({'success': False, 'message': '请先初始化AI'})
    
    data = request.json
    query = data.get('query', '').strip()
    
    if not query:
        return jsonify({'success': False, 'message': '请输入搜索内容'})
    
    if search_session.is_searching:
        return jsonify({'success': False, 'message': '搜索正在进行中'})
    
    # 重置搜索状态
    search_session.is_searching = True
    search_session.result = ""
    search_session.error = ""
    search_session.progress = 0
    
    # 在后台线程中执行搜索
    thread = threading.Thread(target=perform_search, args=(session_id, query))
    thread.daemon = True
    thread.start()
    
    return jsonify({'success': True, 'message': '搜索已开始'})

def perform_search(session_id, query):
    search_session = search_sessions[session_id]
    
    try:
        search_session.progress = 25
        prompt = f"请搜索并告诉我{query}的详细信息，包括不同配置的价格。"
        
        search_session.progress = 50
        result = search_session.agent.run(prompt)
        
        search_session.progress = 100
        search_session.result = result
        search_session.is_searching = False
        
    except Exception as e:
        search_session.error = f"搜索过程中出现错误: {str(e)}"
        search_session.is_searching = False
        search_session.progress = 0

@app.route('/status')
def get_status():
    session_id = session['session_id']
    search_session = search_sessions[session_id]
    
    return jsonify({
        'is_searching': search_session.is_searching,
        'progress': search_session.progress,
        'result': search_session.result,
        'error': search_session.error,
        'has_agent': search_session.agent is not None
    })

@app.route('/stop', methods=['POST'])
def stop_search():
    session_id = session['session_id']
    search_session = search_sessions[session_id]
    
    search_session.is_searching = False
    search_session.progress = 0
    
    return jsonify({'success': True, 'message': '搜索已停止'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)