"""
GPU多模型管理平台主应用程序
提供Web界面和API接口
"""

import os
import sys
import json
import logging
from datetime import datetime
from dataclasses import asdict
from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from flask_cors import CORS
import threading
import time

# 添加核心模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

from core.gpu_detector import gpu_detector
from core.vgpu_manager import vgpu_manager, VGPUStatus
from core.model_manager import model_manager, ModelType, ModelFramework, ModelStatus
from core.scheduler import resource_scheduler, TaskType, SchedulingPolicy
from core.maas_service import MAASService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gpu_platform.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.secret_key = 'gpu_platform_secret_key_2024'
CORS(app)

# 初始化MAAS服务
maas_service = MAASService(model_manager)

# 全局状态
platform_status = {
    'initialized': False,
    'start_time': None,
    'version': '1.0.0'
}

def initialize_platform():
    """初始化平台"""
    try:
        logger.info("正在初始化GPU多模型管理平台...")
        
        # 启动GPU监控
        gpu_detector.start_monitoring(interval=10)
        
        # 启动vGPU监控
        vgpu_manager.start_monitoring(interval=15)
        
        # 启动模型监控
        model_manager.start_monitoring(interval=30)
        
        # 启动资源调度器
        resource_scheduler.start_scheduling(interval=5)
        
        platform_status['initialized'] = True
        platform_status['start_time'] = datetime.now()
        
        logger.info("GPU多模型管理平台初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"平台初始化失败: {e}")
        return False

def shutdown_platform():
    """关闭平台"""
    try:
        logger.info("正在关闭GPU多模型管理平台...")
        
        # 停止调度器
        resource_scheduler.stop_scheduling()
        
        # 停止监控
        model_manager.stop_monitoring()
        vgpu_manager.stop_monitoring()
        gpu_detector.stop_monitoring()
        
        # 清理资源
        # 卸载所有模型实例
        instances = model_manager.list_instances()
        for instance in instances:
            if instance.status in [ModelStatus.LOADED, ModelStatus.LOADING]:
                model_manager.unload_model(instance.instance_id)
        
        # 释放所有vGPU
        vgpus = vgpu_manager.list_vgpus()
        for vgpu in vgpus:
            if vgpu.status != VGPUStatus.UNLOADED:
                vgpu_manager.deallocate_vgpu(vgpu.vgpu_id)
        
        platform_status['initialized'] = False
        
        logger.info("GPU多模型管理平台已关闭")
        
    except Exception as e:
        logger.error(f"平台关闭失败: {e}")

# Web路由
@app.route('/')
def index():
    """主页"""
    if not platform_status['initialized']:
        return render_template('setup.html')
    return render_template('dashboard.html')

@app.route('/maas')
def maas_management():
    """MAAS服务管理页面"""
    return render_template('maas_management.html')

@app.route('/api/status')
def api_status():
    """获取平台状态"""
    try:
        gpu_info = gpu_detector.to_dict()
        vgpu_info = vgpu_manager.to_dict()
        model_info = model_manager.to_dict()
        scheduler_info = resource_scheduler.to_dict()
        
        return jsonify({
            'success': True,
            'data': {
                'platform': platform_status,
                'gpu': gpu_info,
                'vgpu': vgpu_info,
                'models': model_info,
                'scheduler': scheduler_info
            }
        })
    except Exception as e:
        logger.error(f"获取状态失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/gpu/list')
def api_gpu_list():
    """获取GPU列表"""
    try:
        gpu_infos = gpu_detector.get_all_gpu_info()
        return jsonify({
            'success': True,
            'data': [gpu.__dict__ for gpu in gpu_infos]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/system/info')
def api_system_info():
    """获取系统信息"""
    try:
        system_info = gpu_detector.get_system_info()
        if system_info:
            # 序列化datetime对象
            system_dict = asdict(system_info)
            system_dict['timestamp'] = system_info.timestamp.isoformat()
            system_dict['boot_time'] = system_info.boot_time.isoformat()

            return jsonify({
                'success': True,
                'data': system_dict
            })
        else:
            return jsonify({
                'success': False,
                'error': '无法获取系统信息，psutil不可用'
            }), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/vgpu/create', methods=['POST'])
def api_vgpu_create():
    """创建vGPU"""
    try:
        data = request.get_json()
        
        # 创建分配请求
        request_id = vgpu_manager.create_allocation_request(
            requester=data.get('requester', 'web_user'),
            memory_mb=data.get('memory_mb', 1024),
            compute_percent=data.get('compute_percent', 25.0),
            bandwidth_percent=data.get('bandwidth_percent', 25.0),
            max_processes=data.get('max_processes', 1),
            priority=data.get('priority', 5),
            preferred_gpu=data.get('preferred_gpu'),
            tags=data.get('tags', {})
        )
        
        # 分配vGPU
        vgpu_id = vgpu_manager.allocate_vgpu(request_id, data.get('name'))
        
        if vgpu_id:
            return jsonify({
                'success': True,
                'data': {
                    'vgpu_id': vgpu_id,
                    'request_id': request_id
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': '资源不足，无法创建vGPU'
            }), 400
            
    except Exception as e:
        logger.error(f"创建vGPU失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/vgpu/list')
def api_vgpu_list():
    """获取vGPU列表"""
    try:
        vgpus = vgpu_manager.list_vgpus()
        return jsonify({
            'success': True,
            'data': [vgpu_manager._serialize_for_json(vgpu.__dict__) for vgpu in vgpus]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/vgpu/<vgpu_id>/delete', methods=['DELETE'])
def api_vgpu_delete(vgpu_id):
    """删除vGPU"""
    try:
        success = vgpu_manager.deallocate_vgpu(vgpu_id)
        return jsonify({
            'success': success,
            'message': 'vGPU删除成功' if success else 'vGPU删除失败'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/models/register', methods=['POST'])
def api_model_register():
    """注册模型"""
    try:
        data = request.get_json()

        # 检查是否是Ollama模型的快速注册
        if data.get('framework') == 'ollama' and 'ollama_model_name' in data:
            model_id = model_manager.register_ollama_model(
                model_name=data['ollama_model_name'],
                description=data.get('description', ''),
                memory_requirement=data.get('memory_requirement', 4096),
                compute_requirement=data.get('compute_requirement', 50.0),
                tags=data.get('tags', {})
            )

            return jsonify({
                'success': True,
                'data': {'model_id': model_id},
                'message': 'Ollama模型注册成功'
            })

        # 常规模型注册
        model_id = model_manager.register_model(
            name=data['name'],
            model_type=ModelType(data['model_type']),
            framework=ModelFramework(data['framework']),
            model_path=data['model_path'],
            version=data.get('version', '1.0.0'),
            memory_requirement=data.get('memory_requirement', 1024),
            compute_requirement=data.get('compute_requirement', 25.0),
            max_batch_size=data.get('max_batch_size', 1),
            max_sequence_length=data.get('max_sequence_length', 2048),
            precision=data.get('precision', 'fp16'),
            quantization=data.get('quantization'),
            description=data.get('description', '')
        )

        return jsonify({
            'success': True,
            'data': {'model_id': model_id},
            'message': '模型注册成功'
        })

    except Exception as e:
        logger.error(f"注册模型失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/models/list')
def api_model_list():
    """获取模型列表"""
    try:
        models = model_manager.list_models()
        models_with_status = []

        for model in models:
            model_dict = model.__dict__.copy()
            model_dict['status'] = model.get_status(model_manager.model_instances)
            models_with_status.append(model_dict)

        return jsonify({
            'success': True,
            'data': [model_manager._serialize_for_json(model_dict) for model_dict in models_with_status]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/models/ollama/available')
def api_ollama_available_models():
    """获取可用的Ollama模型列表"""
    try:
        import subprocess
        import json as json_lib

        # 检查Ollama是否可用
        try:
            subprocess.run(["ollama", "--version"], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            return jsonify({
                'success': False,
                'error': 'Ollama未安装或不在PATH中'
            }), 400

        # 获取本地已安装的模型
        result = subprocess.run(
            ["ollama", "list"],
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            return jsonify({
                'success': False,
                'error': f'获取Ollama模型列表失败: {result.stderr}'
            }), 500

        # 解析输出
        lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
        models = []

        for line in lines:
            if line.strip():
                parts = line.split()
                if len(parts) >= 3:
                    model_name = parts[0]
                    model_id = parts[1] if len(parts) > 1 else ""
                    size = parts[2] if len(parts) > 2 else ""
                    modified = " ".join(parts[3:]) if len(parts) > 3 else ""

                    models.append({
                        'name': model_name,
                        'id': model_id,
                        'size': size,
                        'modified': modified
                    })

        return jsonify({
            'success': True,
            'data': {
                'installed_models': models,
                'popular_models': [
                    'llama2:7b', 'llama2:13b', 'llama2:70b',
                    'codellama:7b', 'codellama:13b', 'codellama:34b',
                    'mistral:7b', 'mixtral:8x7b',
                    'phi:2.7b', 'gemma:2b', 'gemma:7b',
                    'qwen:7b', 'qwen:14b', 'qwen:72b',
                    'vicuna:7b', 'vicuna:13b', 'vicuna:33b'
                ]
            }
        })

    except Exception as e:
        logger.error(f"获取Ollama模型列表失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/models/<model_id>/load', methods=['POST'])
def api_model_load(model_id):
    """加载模型"""
    try:
        data = request.get_json()
        vgpu_id = data.get('vgpu_id')
        port = data.get('port')
        
        if not vgpu_id:
            return jsonify({
                'success': False,
                'error': '必须指定vGPU ID'
            }), 400
        
        instance_id = model_manager.load_model(model_id, vgpu_id, port)
        
        if instance_id:
            return jsonify({
                'success': True,
                'data': {'instance_id': instance_id}
            })
        else:
            return jsonify({
                'success': False,
                'error': '模型加载失败'
            }), 500
            
    except Exception as e:
        logger.error(f"加载模型失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/models/instances')
def api_model_instances():
    """获取模型实例列表"""
    try:
        instances = model_manager.list_instances()
        return jsonify({
            'success': True,
            'data': [model_manager._serialize_for_json(instance.__dict__) for instance in instances]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/models/instances/<instance_id>/unload', methods=['POST'])
def api_model_unload(instance_id):
    """卸载模型实例"""
    try:
        success = model_manager.unload_model(instance_id)
        return jsonify({
            'success': success,
            'message': '模型卸载成功' if success else '模型卸载失败'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/scheduler/submit', methods=['POST'])
def api_scheduler_submit():
    """提交调度任务"""
    try:
        data = request.get_json()
        
        task_id = resource_scheduler.submit_task(
            task_type=TaskType(data['task_type']),
            user_id=data.get('user_id', 'web_user'),
            resource_requirements=data['resource_requirements'],
            priority=data.get('priority', 5),
            estimated_duration=data.get('estimated_duration'),
            metadata=data.get('metadata', {})
        )
        
        return jsonify({
            'success': True,
            'data': {'task_id': task_id}
        })
        
    except Exception as e:
        logger.error(f"提交任务失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/scheduler/tasks')
def api_scheduler_tasks():
    """获取调度任务状态"""
    try:
        queue_status = resource_scheduler.get_queue_status()
        return jsonify({
            'success': True,
            'data': queue_status
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/scheduler/task/<task_id>')
def api_scheduler_task_status(task_id):
    """获取特定任务状态"""
    try:
        task = resource_scheduler.get_task_status(task_id)
        if task:
            return jsonify({
                'success': True,
                'data': task.__dict__
            })
        else:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/scheduler/task/<task_id>/cancel', methods=['POST'])
def api_scheduler_cancel_task(task_id):
    """取消任务"""
    try:
        success = resource_scheduler.cancel_task(task_id)
        return jsonify({
            'success': success,
            'message': '任务取消成功' if success else '任务取消失败'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/initialize', methods=['POST'])
def api_initialize():
    """初始化平台"""
    try:
        if platform_status['initialized']:
            return jsonify({
                'success': True,
                'message': '平台已经初始化'
            })
        
        success = initialize_platform()
        return jsonify({
            'success': success,
            'message': '平台初始化成功' if success else '平台初始化失败'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/shutdown', methods=['POST'])
def api_shutdown():
    """关闭平台"""
    try:
        shutdown_platform()
        return jsonify({
            'success': True,
            'message': '平台关闭成功'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'error': 'API端点不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': '内部服务器错误'}), 500

# 启动函数
def run_platform(host='0.0.0.0', port=5000, debug=False):
    """运行平台"""
    try:
        logger.info(f"启动GPU多模型管理平台 v{platform_status['version']}")
        logger.info(f"Web界面地址: http://{host}:{port}")
        
        # 自动初始化平台
        if initialize_platform():
            logger.info("平台自动初始化成功")
        else:
            logger.warning("平台自动初始化失败，请手动初始化")
        
        # 启动Flask应用
        app.run(host=host, port=port, debug=debug, threaded=True)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭平台...")
        shutdown_platform()
    except Exception as e:
        logger.error(f"平台运行错误: {e}")
        shutdown_platform()

# 注册API蓝图
from api.openai_compatible import openai_api
from api.maas_management import maas_mgmt

app.register_blueprint(openai_api)
app.register_blueprint(maas_mgmt)

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='GPU多模型管理平台')
    parser.add_argument('--host', default='0.0.0.0', help='绑定主机地址')
    parser.add_argument('--port', type=int, default=5000, help='绑定端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')

    args = parser.parse_args()

    run_platform(host=args.host, port=args.port, debug=args.debug)
