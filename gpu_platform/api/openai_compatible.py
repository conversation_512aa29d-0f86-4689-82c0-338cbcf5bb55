"""
OpenAI兼容的API接口
提供标准的OpenAI API格式，兼容现有的客户端库
"""

from flask import Blueprint, request, jsonify, Response
from functools import wraps
import json
import time
import uuid
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# 创建蓝图
openai_api = Blueprint('openai_api', __name__, url_prefix='/v1')

def require_api_key(f):
    """API密钥验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 从请求头获取API密钥
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'error': {
                    'message': 'Missing or invalid API key',
                    'type': 'authentication_error',
                    'code': 'invalid_api_key'
                }
            }), 401
            
        api_key = auth_header[7:]  # 移除 "Bearer " 前缀
        
        # 验证API密钥
        from app import maas_service
        validated_key = maas_service.validate_api_key(api_key)
        if not validated_key:
            return jsonify({
                'error': {
                    'message': 'Invalid API key',
                    'type': 'authentication_error',
                    'code': 'invalid_api_key'
                }
            }), 401
            
        # 将验证后的密钥信息传递给视图函数
        request.api_key = validated_key
        return f(*args, **kwargs)
    return decorated_function

@openai_api.route('/models', methods=['GET'])
@require_api_key
def list_models():
    """列出可用模型"""
    try:
        from app import maas_service
        models = maas_service.get_available_models(request.api_key)
        
        # 转换为OpenAI格式
        openai_models = []
        for model in models:
            openai_models.append({
                'id': model['id'],
                'object': 'model',
                'created': int(time.time()),
                'owned_by': 'gpu-platform',
                'permission': [],
                'root': model['id'],
                'parent': None
            })
            
        return jsonify({
            'object': 'list',
            'data': openai_models
        })
        
    except Exception as e:
        logger.error(f"列出模型失败: {e}")
        return jsonify({
            'error': {
                'message': 'Internal server error',
                'type': 'server_error',
                'code': 'internal_error'
            }
        }), 500

@openai_api.route('/chat/completions', methods=['POST'])
@require_api_key
def chat_completions():
    """聊天完成接口"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'error': {
                    'message': 'Invalid JSON in request body',
                    'type': 'invalid_request_error',
                    'code': 'invalid_json'
                }
            }), 400
            
        # 验证必需参数
        model_id = data.get('model')
        messages = data.get('messages', [])
        
        if not model_id:
            return jsonify({
                'error': {
                    'message': 'Missing required parameter: model',
                    'type': 'invalid_request_error',
                    'code': 'missing_parameter'
                }
            }), 400
            
        if not messages:
            return jsonify({
                'error': {
                    'message': 'Missing required parameter: messages',
                    'type': 'invalid_request_error',
                    'code': 'missing_parameter'
                }
            }), 400
            
        # 检查模型访问权限
        from app import maas_service
        from core.model_manager import model_manager
        if not maas_service.check_model_access(request.api_key, model_id):
            return jsonify({
                'error': {
                    'message': f'Model {model_id} not accessible with this API key',
                    'type': 'permission_error',
                    'code': 'model_not_accessible'
                }
            }), 403
            
        # 检查模型是否存在且启用API
        if model_id not in model_manager.models:
            return jsonify({
                'error': {
                    'message': f'Model {model_id} not found',
                    'type': 'invalid_request_error',
                    'code': 'model_not_found'
                }
            }), 404
            
        model = model_manager.models[model_id]
        if not model.api_enabled:
            return jsonify({
                'error': {
                    'message': f'Model {model_id} API not enabled',
                    'type': 'invalid_request_error',
                    'code': 'model_not_available'
                }
            }), 400
            
        # 获取其他参数
        max_tokens = data.get('max_tokens', 1000)
        temperature = data.get('temperature', 0.7)
        stream = data.get('stream', False)
        
        # 估算输入token数（简单估算：每4个字符约1个token）
        input_text = ' '.join([msg.get('content', '') for msg in messages])
        input_tokens = len(input_text) // 4
        
        # 检查速率限制和使用额度
        if not maas_service.process_request(request.api_key, model_id, input_tokens, 0):
            return jsonify({
                'error': {
                    'message': 'Rate limit exceeded or usage quota exceeded',
                    'type': 'rate_limit_error',
                    'code': 'rate_limit_exceeded'
                }
            }), 429
            
        # 这里应该调用实际的模型推理
        # 目前返回模拟响应
        response_text = f"这是来自模型 {model.name} 的响应。收到了 {len(messages)} 条消息。"
        output_tokens = len(response_text) // 4
        
        # 记录实际使用量
        maas_service.process_request(request.api_key, model_id, input_tokens, output_tokens)
        
        if stream:
            return stream_response(model_id, response_text, input_tokens, output_tokens)
        else:
            return jsonify({
                'id': f'chatcmpl-{uuid.uuid4().hex}',
                'object': 'chat.completion',
                'created': int(time.time()),
                'model': model_id,
                'choices': [{
                    'index': 0,
                    'message': {
                        'role': 'assistant',
                        'content': response_text
                    },
                    'finish_reason': 'stop'
                }],
                'usage': {
                    'prompt_tokens': input_tokens,
                    'completion_tokens': output_tokens,
                    'total_tokens': input_tokens + output_tokens
                }
            })
            
    except Exception as e:
        logger.error(f"聊天完成失败: {e}")
        return jsonify({
            'error': {
                'message': 'Internal server error',
                'type': 'server_error',
                'code': 'internal_error'
            }
        }), 500

def stream_response(model_id: str, response_text: str, input_tokens: int, output_tokens: int):
    """流式响应"""
    def generate():
        completion_id = f'chatcmpl-{uuid.uuid4().hex}'
        
        # 发送开始事件
        yield f"data: {json.dumps({
            'id': completion_id,
            'object': 'chat.completion.chunk',
            'created': int(time.time()),
            'model': model_id,
            'choices': [{
                'index': 0,
                'delta': {'role': 'assistant', 'content': ''},
                'finish_reason': None
            }]
        })}\n\n"
        
        # 逐字发送响应
        for i, char in enumerate(response_text):
            yield f"data: {json.dumps({
                'id': completion_id,
                'object': 'chat.completion.chunk',
                'created': int(time.time()),
                'model': model_id,
                'choices': [{
                    'index': 0,
                    'delta': {'content': char},
                    'finish_reason': None
                }]
            })}\n\n"
            
        # 发送结束事件
        yield f"data: {json.dumps({
            'id': completion_id,
            'object': 'chat.completion.chunk',
            'created': int(time.time()),
            'model': model_id,
            'choices': [{
                'index': 0,
                'delta': {},
                'finish_reason': 'stop'
            }],
            'usage': {
                'prompt_tokens': input_tokens,
                'completion_tokens': output_tokens,
                'total_tokens': input_tokens + output_tokens
            }
        })}\n\n"
        
        yield "data: [DONE]\n\n"
        
    return Response(generate(), mimetype='text/plain')

@openai_api.route('/completions', methods=['POST'])
@require_api_key
def completions():
    """文本完成接口"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'error': {
                    'message': 'Invalid JSON in request body',
                    'type': 'invalid_request_error',
                    'code': 'invalid_json'
                }
            }), 400
            
        model_id = data.get('model')
        prompt = data.get('prompt', '')
        
        if not model_id:
            return jsonify({
                'error': {
                    'message': 'Missing required parameter: model',
                    'type': 'invalid_request_error',
                    'code': 'missing_parameter'
                }
            }), 400
            
        # 类似chat/completions的处理逻辑
        from app import maas_service
        from core.model_manager import model_manager

        if not maas_service.check_model_access(request.api_key, model_id):
            return jsonify({
                'error': {
                    'message': f'Model {model_id} not accessible with this API key',
                    'type': 'permission_error',
                    'code': 'model_not_accessible'
                }
            }), 403
            
        input_tokens = len(prompt) // 4
        response_text = f"基于提示 '{prompt[:50]}...' 的完成响应"
        output_tokens = len(response_text) // 4
        
        if not maas_service.process_request(request.api_key, model_id, input_tokens, output_tokens):
            return jsonify({
                'error': {
                    'message': 'Rate limit exceeded or usage quota exceeded',
                    'type': 'rate_limit_error',
                    'code': 'rate_limit_exceeded'
                }
            }), 429
            
        return jsonify({
            'id': f'cmpl-{uuid.uuid4().hex}',
            'object': 'text_completion',
            'created': int(time.time()),
            'model': model_id,
            'choices': [{
                'text': response_text,
                'index': 0,
                'logprobs': None,
                'finish_reason': 'stop'
            }],
            'usage': {
                'prompt_tokens': input_tokens,
                'completion_tokens': output_tokens,
                'total_tokens': input_tokens + output_tokens
            }
        })
        
    except Exception as e:
        logger.error(f"文本完成失败: {e}")
        return jsonify({
            'error': {
                'message': 'Internal server error',
                'type': 'server_error',
                'code': 'internal_error'
            }
        }), 500
