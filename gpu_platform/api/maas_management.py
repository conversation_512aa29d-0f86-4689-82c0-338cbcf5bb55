"""
MAAS服务管理API
提供API密钥管理、使用统计等功能
"""

from flask import Blueprint, request, jsonify
import logging

logger = logging.getLogger(__name__)

# 创建蓝图
maas_mgmt = Blueprint('maas_mgmt', __name__, url_prefix='/api/maas')

@maas_mgmt.route('/keys', methods=['GET'])
def list_api_keys():
    """列出所有API密钥"""
    try:
        from app import maas_service
        keys = maas_service.list_api_keys()
        return jsonify({
            'success': True,
            'data': keys
        })
    except Exception as e:
        logger.error(f"列出API密钥失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@maas_mgmt.route('/keys', methods=['POST'])
def create_api_key():
    """创建新的API密钥"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请提供有效的JSON数据'
            }), 400
            
        name = data.get('name')
        if not name:
            return jsonify({
                'success': False,
                'error': '密钥名称不能为空'
            }), 400
            
        model_access = data.get('model_access', [])
        rate_limit = data.get('rate_limit', 1000)
        usage_limit = data.get('usage_limit')
        expires_days = data.get('expires_days')
        
        from app import maas_service
        key_id, raw_key = maas_service.generate_api_key(
            name=name,
            model_access=model_access,
            rate_limit=rate_limit,
            usage_limit=usage_limit,
            expires_days=expires_days
        )
        
        return jsonify({
            'success': True,
            'data': {
                'key_id': key_id,
                'api_key': raw_key,
                'warning': '请妥善保存API密钥，系统不会再次显示完整密钥'
            }
        })
        
    except Exception as e:
        logger.error(f"创建API密钥失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@maas_mgmt.route('/keys/<key_id>', methods=['GET'])
def get_api_key_info(key_id):
    """获取API密钥详细信息"""
    try:
        from app import maas_service
        key_info = maas_service.get_api_key_info(key_id)
        
        if not key_info:
            return jsonify({
                'success': False,
                'error': 'API密钥不存在'
            }), 404
            
        return jsonify({
            'success': True,
            'data': key_info
        })
        
    except Exception as e:
        logger.error(f"获取API密钥信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@maas_mgmt.route('/keys/<key_id>', methods=['PUT'])
def update_api_key(key_id):
    """更新API密钥配置"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请提供有效的JSON数据'
            }), 400
            
        from app import maas_service

        if key_id not in maas_service.api_keys:
            return jsonify({
                'success': False,
                'error': 'API密钥不存在'
            }), 404
            
        api_key = maas_service.api_keys[key_id]
        
        # 更新可修改的字段
        if 'name' in data:
            api_key.name = data['name']
        if 'model_access' in data:
            api_key.model_access = data['model_access']
        if 'rate_limit' in data:
            api_key.rate_limit = data['rate_limit']
        if 'usage_limit' in data:
            api_key.usage_limit = data['usage_limit']
        if 'is_active' in data:
            api_key.is_active = data['is_active']
            
        return jsonify({
            'success': True,
            'message': 'API密钥更新成功'
        })
        
    except Exception as e:
        logger.error(f"更新API密钥失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@maas_mgmt.route('/keys/<key_id>', methods=['DELETE'])
def delete_api_key(key_id):
    """删除API密钥"""
    try:
        from app import maas_service

        if maas_service.delete_api_key(key_id):
            return jsonify({
                'success': True,
                'message': 'API密钥删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'API密钥不存在'
            }), 404
            
    except Exception as e:
        logger.error(f"删除API密钥失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@maas_mgmt.route('/keys/<key_id>/revoke', methods=['POST'])
def revoke_api_key(key_id):
    """撤销API密钥"""
    try:
        from app import maas_service

        if maas_service.revoke_api_key(key_id):
            return jsonify({
                'success': True,
                'message': 'API密钥撤销成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'API密钥不存在'
            }), 404
            
    except Exception as e:
        logger.error(f"撤销API密钥失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@maas_mgmt.route('/usage/<key_id>', methods=['GET'])
def get_usage_stats(key_id):
    """获取使用统计"""
    try:
        days = request.args.get('days', 30, type=int)
        
        from app import maas_service
        usage = maas_service.usage_tracker.get_usage(key_id, days)
        
        return jsonify({
            'success': True,
            'data': usage
        })
        
    except Exception as e:
        logger.error(f"获取使用统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@maas_mgmt.route('/models/available', methods=['GET'])
def get_available_models():
    """获取可用于API的模型列表"""
    try:
        from core.model_manager import model_manager

        models = []
        for model_id, config in model_manager.model_configs.items():
            models.append({
                'model_id': model_id,
                'name': config.name,
                'description': config.description,
                'type': config.model_type.value,
                'framework': config.framework.value,
                'api_enabled': getattr(config, 'api_enabled', False),
                'pricing': getattr(config, 'pricing', {'input_tokens': 0.0, 'output_tokens': 0.0}),
                'base_url': getattr(config, 'base_url', None)
            })

        return jsonify({
            'success': True,
            'data': models
        })

    except Exception as e:
        logger.error(f"获取可用模型失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@maas_mgmt.route('/models/<model_id>/api', methods=['PUT'])
def update_model_api_config(model_id):
    """更新模型API配置"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请提供有效的JSON数据'
            }), 400

        from core.model_manager import model_manager

        if model_id not in model_manager.model_configs:
            return jsonify({
                'success': False,
                'error': '模型不存在'
            }), 404

        config = model_manager.model_configs[model_id]

        # 更新API配置
        if 'api_enabled' in data:
            config.api_enabled = data['api_enabled']
        if 'api_key' in data:
            config.api_key = data['api_key']
        if 'base_url' in data:
            config.base_url = data['base_url']
        if 'pricing' in data:
            config.pricing = data['pricing']

        # 保存配置
        model_manager.save_models()

        return jsonify({
            'success': True,
            'message': '模型API配置更新成功'
        })

    except Exception as e:
        logger.error(f"更新模型API配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@maas_mgmt.route('/stats/overview', methods=['GET'])
def get_overview_stats():
    """获取总体统计信息"""
    try:
        from app import maas_service
        from core.model_manager import model_manager

        # 统计API密钥
        total_keys = len(maas_service.api_keys)
        active_keys = sum(1 for key in maas_service.api_keys.values() if key.is_active)

        # 统计模型
        total_models = len(model_manager.model_configs)
        api_enabled_models = sum(1 for config in model_manager.model_configs.values()
                               if getattr(config, 'api_enabled', False))

        # 统计使用量（最近30天）
        total_requests = 0
        total_cost = 0.0

        for key_id in maas_service.api_keys.keys():
            usage = maas_service.usage_tracker.get_usage(key_id, 30)
            total_requests += usage['total_requests']
            total_cost += usage['total_cost']

        return jsonify({
            'success': True,
            'data': {
                'api_keys': {
                    'total': total_keys,
                    'active': active_keys,
                    'inactive': total_keys - active_keys
                },
                'models': {
                    'total': total_models,
                    'api_enabled': api_enabled_models,
                    'api_disabled': total_models - api_enabled_models
                },
                'usage_30_days': {
                    'total_requests': total_requests,
                    'total_cost': total_cost
                }
            }
        })

    except Exception as e:
        logger.error(f"获取总体统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
