#!/usr/bin/env python3
"""
Ollama集成测试脚本
测试GPU平台的Ollama功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_ollama_available_models():
    """测试获取可用Ollama模型"""
    print("🔍 测试获取可用Ollama模型...")
    
    response = requests.get(f"{BASE_URL}/api/models/ollama/available")
    result = response.json()
    
    if result['success']:
        print("✅ 成功获取Ollama模型列表")
        print(f"   已安装模型: {len(result['data']['installed_models'])} 个")
        print(f"   热门模型: {len(result['data']['popular_models'])} 个")
        
        if result['data']['installed_models']:
            print("   已安装的模型:")
            for model in result['data']['installed_models']:
                print(f"     - {model['name']} ({model['size']})")
    else:
        print(f"❌ 获取Ollama模型列表失败: {result['error']}")
    
    return result['success']

def test_ollama_model_registration():
    """测试Ollama模型注册"""
    print("\n📝 测试Ollama模型注册...")
    
    # 注册一个热门模型
    model_data = {
        "framework": "ollama",
        "ollama_model_name": "llama2:7b",
        "description": "Llama 2 7B模型 - 通过Ollama运行",
        "memory_requirement": 8192,
        "compute_requirement": 70.0
    }
    
    response = requests.post(
        f"{BASE_URL}/api/models/register",
        headers={"Content-Type": "application/json"},
        data=json.dumps(model_data)
    )
    
    result = response.json()
    
    if result['success']:
        print("✅ Ollama模型注册成功")
        print(f"   模型ID: {result['data']['model_id']}")
        print(f"   消息: {result['message']}")
        return result['data']['model_id']
    else:
        print(f"❌ Ollama模型注册失败: {result['error']}")
        return None

def test_model_list():
    """测试模型列表"""
    print("\n📋 测试模型列表...")
    
    response = requests.get(f"{BASE_URL}/api/models/list")
    result = response.json()
    
    if result['success']:
        print("✅ 成功获取模型列表")
        print(f"   总模型数: {len(result['data'])} 个")
        
        ollama_models = [m for m in result['data'] if m['framework'] == 'ModelFramework.OLLAMA']
        print(f"   Ollama模型数: {len(ollama_models)} 个")
        
        for model in ollama_models:
            print(f"     - {model['name']} ({model['model_path']})")
            print(f"       内存需求: {model['memory_requirement']}MB")
            print(f"       计算需求: {model['compute_requirement']}%")
    else:
        print(f"❌ 获取模型列表失败: {result['error']}")
    
    return result['success']

def test_gpu_detection():
    """测试GPU检测"""
    print("\n🖥️  测试GPU检测...")

    response = requests.get(f"{BASE_URL}/api/gpu/list")
    result = response.json()
    
    if result['success']:
        print("✅ 成功获取GPU信息")
        print(f"   GPU数量: {len(result['data'])} 个")
        
        for gpu in result['data']:
            print(f"     - {gpu['name']}")
            print(f"       显存: {gpu['memory_total']}MB")
            print(f"       温度: {gpu['temperature']}°C")
            print(f"       功耗: {gpu['power_usage']}W")
    else:
        print(f"❌ 获取GPU信息失败: {result['error']}")
    
    return result['success']

def test_vgpu_management():
    """测试vGPU管理"""
    print("\n🔧 测试vGPU管理...")

    response = requests.get(f"{BASE_URL}/api/vgpu/list")
    result = response.json()
    
    if result['success']:
        print("✅ 成功获取vGPU信息")
        print(f"   vGPU数量: {len(result['data'])} 个")
        
        for vgpu in result['data']:
            print(f"     - {vgpu['vgpu_id'][:8]}...")
            print(f"       内存分配: {vgpu['memory_allocated']}MB")
            print(f"       计算分配: {vgpu['compute_allocated']}%")
            print(f"       状态: {vgpu['status']}")
    else:
        print(f"❌ 获取vGPU信息失败: {result['error']}")
    
    return result['success']

def main():
    """主测试函数"""
    print("🚀 开始Ollama集成测试")
    print("=" * 50)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    # 运行测试
    tests = [
        test_gpu_detection,
        test_vgpu_management,
        test_ollama_available_models,
        test_ollama_model_registration,
        test_model_list
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Ollama集成功能正常")
    else:
        print("⚠️  部分测试失败，请检查日志")

if __name__ == "__main__":
    main()
