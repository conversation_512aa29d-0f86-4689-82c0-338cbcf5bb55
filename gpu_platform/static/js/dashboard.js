// GPU多模型管理平台前端JavaScript

// 全局变量
let platformData = {};
let refreshInterval = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    startAutoRefresh();
});

// 初始化仪表板
function initializeDashboard() {
    refreshData();
    loadModelOptions();
    loadVGPUOptions();
}

// 显示指定部分
function showSection(sectionId) {
    // 隐藏所有部分
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
        section.style.display = 'none';
    });

    // 显示指定部分
    document.getElementById(sectionId).style.display = 'block';

    // 更新导航状态
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    event.target.classList.add('active');

    // 根据部分刷新数据
    switch(sectionId) {
        case 'gpu-management':
            loadGPUDetails();
            break;
        case 'vgpu-management':
            loadVGPUList();
            break;
        case 'model-management':
            loadModelList();
            loadModelInstances();
            break;
        case 'task-scheduler':
            loadTaskList();
            break;
    }
}

// 刷新所有数据
async function refreshData() {
    try {
        const response = await fetch('/api/status');
        const result = await response.json();

        if (result.success) {
            platformData = result.data;
            updateDashboard();
        } else {
            showAlert('获取状态失败: ' + result.error, 'danger');
        }
    } catch (error) {
        showAlert('网络错误: ' + error.message, 'danger');
    }
}

// 更新仪表板显示
function updateDashboard() {
    updateOverviewCards();
    updateGPUOverview();
}

// 更新概览卡片
function updateOverviewCards() {
    const gpuData = platformData.gpu || {};
    const vgpuData = platformData.vgpu || {};
    const modelData = platformData.models || {};
    const schedulerData = platformData.scheduler || {};

    // GPU数量
    document.getElementById('gpu-count').textContent = gpuData.gpu_count || 0;

    // vGPU数量
    const vgpuInstances = vgpuData.vgpu_instances || {};
    document.getElementById('vgpu-count').textContent = Object.keys(vgpuInstances).length;

    // 模型数量
    const modelInstances = modelData.model_instances || {};
    const loadedModels = Object.values(modelInstances).filter(m => m.status === 'loaded').length;
    document.getElementById('model-count').textContent = loadedModels;

    // 任务数量
    const queueStatus = schedulerData.queue_status || {};
    document.getElementById('task-count').textContent = queueStatus.running_tasks || 0;
}

// 更新GPU概览
function updateGPUOverview() {
    const gpuData = platformData.gpu || {};
    const gpus = gpuData.gpus || [];
    const container = document.getElementById('gpu-overview');

    container.innerHTML = '';

    gpus.forEach((gpu, index) => {
        const memoryUsage = (gpu.memory_used / gpu.memory_total * 100).toFixed(1);
        const tempColor = gpu.temperature > 80 ? 'danger' : gpu.temperature > 70 ? 'warning' : 'success';

        const gpuCard = `
            <div class="col-md-6 mb-3">
                <div class="card gpu-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="card-title mb-0">GPU ${gpu.gpu_id}: ${gpu.name}</h6>
                            <span class="badge bg-${tempColor}">${gpu.temperature}°C</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">显存使用率</small>
                            <div class="progress progress-bar-custom">
                                <div class="progress-bar" style="width: ${memoryUsage}%"></div>
                            </div>
                            <small>${gpu.memory_used}MB / ${gpu.memory_total}MB (${memoryUsage}%)</small>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">GPU利用率</small>
                            <div class="progress progress-bar-custom">
                                <div class="progress-bar bg-info" style="width: ${gpu.utilization}%"></div>
                            </div>
                            <small>${gpu.utilization}%</small>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">功耗: ${gpu.power_usage}W / ${gpu.power_limit}W</small>
                            <small class="text-muted">进程: ${gpu.processes.length}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.innerHTML += gpuCard;
    });
}

// 加载GPU详细信息
async function loadGPUDetails() {
    try {
        const response = await fetch('/api/gpu/list');
        const result = await response.json();

        if (result.success) {
            displayGPUDetails(result.data);
        }
    } catch (error) {
        showAlert('加载GPU详细信息失败: ' + error.message, 'danger');
    }
}

// 显示GPU详细信息
function displayGPUDetails(gpus) {
    const container = document.getElementById('gpu-list');
    container.innerHTML = '';

    gpus.forEach(gpu => {
        const processes = gpu.processes.map(p =>
            `<li class="list-group-item d-flex justify-content-between">
                <span>PID: ${p.pid} - ${p.name}</span>
                <span>${p.memory_used}MB</span>
            </li>`
        ).join('');

        const gpuDetail = `
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>GPU ${gpu.gpu_id}: ${gpu.name}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>UUID:</strong> ${gpu.uuid}</p>
                                <p><strong>PCI Bus ID:</strong> ${gpu.pci_bus_id}</p>
                                <p><strong>驱动版本:</strong> ${gpu.driver_version}</p>
                                <p><strong>CUDA版本:</strong> ${gpu.cuda_version}</p>
                                <p><strong>计算能力:</strong> ${gpu.compute_capability[0]}.${gpu.compute_capability[1]}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>显存:</strong> ${gpu.memory_used}MB / ${gpu.memory_total}MB</p>
                                <p><strong>温度:</strong> ${gpu.temperature}°C</p>
                                <p><strong>功耗:</strong> ${gpu.power_usage}W / ${gpu.power_limit}W</p>
                                <p><strong>时钟频率:</strong> ${gpu.clock_graphics}MHz / ${gpu.clock_memory}MHz</p>
                                <p><strong>风扇速度:</strong> ${gpu.fan_speed}%</p>
                            </div>
                        </div>
                        ${processes ? `
                        <div class="mt-3">
                            <h6>运行中的进程:</h6>
                            <ul class="list-group">
                                ${processes}
                            </ul>
                        </div>` : ''}
                    </div>
                </div>
            </div>
        `;
        container.innerHTML += gpuDetail;
    });
}

// 加载vGPU列表
async function loadVGPUList() {
    try {
        const response = await fetch('/api/vgpu/list');
        const result = await response.json();

        if (result.success) {
            displayVGPUList(result.data);
        }
    } catch (error) {
        showAlert('加载vGPU列表失败: ' + error.message, 'danger');
    }
}

// 显示vGPU列表
function displayVGPUList(vgpus) {
    const container = document.getElementById('vgpu-list');
    container.innerHTML = '';

    vgpus.forEach(vgpu => {
        const statusColor = {
            'idle': 'secondary',
            'allocated': 'primary',
            'running': 'success',
            'error': 'danger',
            'maintenance': 'warning'
        }[vgpu.status] || 'secondary';

        const vgpuCard = `
            <div class="col-md-6 mb-3">
                <div class="card vgpu-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="card-title mb-0">${vgpu.name}</h6>
                            <span class="badge bg-${statusColor}">${vgpu.status}</span>
                        </div>
                        <p class="card-text">
                            <small class="text-muted">物理GPU: ${vgpu.physical_gpu_id}</small><br>
                            <small class="text-muted">所有者: ${vgpu.owner}</small><br>
                            <small class="text-muted">显存: ${vgpu.resource.memory_mb}MB</small><br>
                            <small class="text-muted">计算: ${vgpu.resource.compute_percent}%</small><br>
                            <small class="text-muted">带宽: ${vgpu.resource.bandwidth_percent}%</small><br>
                            <small class="text-muted">优先级: ${vgpu.resource.priority}</small>
                        </p>
                        <div class="d-flex justify-content-end">
                            <button class="btn btn-sm btn-danger" onclick="deleteVGPU('${vgpu.vgpu_id}')">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.innerHTML += vgpuCard;
    });
}

// 创建vGPU
async function createVGPU() {
    const formData = {
        name: document.getElementById('vgpu-name').value,
        memory_mb: parseInt(document.getElementById('vgpu-memory').value),
        compute_percent: parseFloat(document.getElementById('vgpu-compute').value),
        bandwidth_percent: parseFloat(document.getElementById('vgpu-bandwidth').value),
        priority: parseInt(document.getElementById('vgpu-priority').value),
        requester: 'web_user'
    };

    try {
        const response = await fetch('/api/vgpu/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            showAlert('vGPU创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('createVGPUModal')).hide();
            document.getElementById('createVGPUForm').reset();
            loadVGPUList();
        } else {
            showAlert('vGPU创建失败: ' + result.error, 'danger');
        }
    } catch (error) {
        showAlert('网络错误: ' + error.message, 'danger');
    }
}

// 删除vGPU
async function deleteVGPU(vgpuId) {
    if (!confirm('确定要删除这个vGPU吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/vgpu/${vgpuId}/delete`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showAlert('vGPU删除成功', 'success');
            loadVGPUList();
        } else {
            showAlert('vGPU删除失败: ' + result.message, 'danger');
        }
    } catch (error) {
        showAlert('网络错误: ' + error.message, 'danger');
    }
}

// 加载模型选项
async function loadModelOptions() {
    try {
        const response = await fetch('/api/models/list');
        const result = await response.json();

        if (result.success) {
            const select = document.getElementById('load-model-select');
            select.innerHTML = '<option value="">请选择模型</option>';

            result.data.forEach(model => {
                const option = document.createElement('option');
                option.value = model.model_id;
                option.textContent = `${model.name} (${model.version})`;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载模型选项失败:', error);
    }
}

// 加载vGPU选项
async function loadVGPUOptions() {
    try {
        const response = await fetch('/api/vgpu/list');
        const result = await response.json();

        if (result.success) {
            const select = document.getElementById('load-vgpu-select');
            select.innerHTML = '<option value="">请选择vGPU</option>';

            result.data.forEach(vgpu => {
                if (vgpu.status === 'allocated' || vgpu.status === 'idle') {
                    const option = document.createElement('option');
                    option.value = vgpu.vgpu_id;
                    option.textContent = `${vgpu.name} (GPU${vgpu.physical_gpu_id}, ${vgpu.resource.memory_mb}MB)`;
                    select.appendChild(option);
                }
            });
        }
    } catch (error) {
        console.error('加载vGPU选项失败:', error);
    }
}

// 显示警告消息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 自动移除警告
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// 开始自动刷新
function startAutoRefresh() {
    refreshInterval = setInterval(refreshData, 10000); // 每10秒刷新一次
}

// 停止自动刷新
function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
}

// 加载模型列表
async function loadModelList() {
    try {
        const response = await fetch('/api/models/list');
        const result = await response.json();

        if (result.success) {
            displayModelList(result.data);
        }
    } catch (error) {
        showAlert('加载模型列表失败: ' + error.message, 'danger');
    }
}

// 显示模型列表
function displayModelList(models) {
    const container = document.getElementById('model-list');

    if (models.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无已注册的模型</p>';
        return;
    }

    const table = `
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>类型</th>
                    <th>框架</th>
                    <th>版本</th>
                    <th>内存需求</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${models.map(model => `
                    <tr>
                        <td>${model.name}</td>
                        <td>${model.model_type}</td>
                        <td>${model.framework}</td>
                        <td>${model.version}</td>
                        <td>${model.memory_requirement}MB</td>
                        <td><span class="badge bg-secondary">${model.status}</span></td>
                        <td>
                            <button class="btn btn-sm btn-primary" onclick="loadModelToVGPU('${model.model_id}')">
                                <i class="bi bi-play"></i> 加载
                            </button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    container.innerHTML = table;
}

// 加载模型实例
async function loadModelInstances() {
    try {
        const response = await fetch('/api/models/instances');
        const result = await response.json();

        if (result.success) {
            displayModelInstances(result.data);
        }
    } catch (error) {
        showAlert('加载模型实例失败: ' + error.message, 'danger');
    }
}

// 显示模型实例
function displayModelInstances(instances) {
    const container = document.getElementById('model-instances');
    container.innerHTML = '';

    if (instances.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无运行中的模型实例</p>';
        return;
    }

    instances.forEach(instance => {
        const statusColor = {
            'loading': 'warning',
            'loaded': 'success',
            'error': 'danger',
            'unloading': 'info'
        }[instance.status] || 'secondary';

        const instanceCard = `
            <div class="col-md-6 mb-3">
                <div class="card model-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="card-title mb-0">${instance.model_name}</h6>
                            <span class="badge bg-${statusColor}">${instance.status}</span>
                        </div>
                        <p class="card-text">
                            <small class="text-muted">实例ID: ${instance.instance_id}</small><br>
                            <small class="text-muted">vGPU: ${instance.vgpu_id}</small><br>
                            <small class="text-muted">端口: ${instance.port || 'N/A'}</small><br>
                            <small class="text-muted">PID: ${instance.process_id || 'N/A'}</small><br>
                            <small class="text-muted">启动时间: ${new Date(instance.start_time).toLocaleString()}</small>
                        </p>
                        <div class="d-flex justify-content-end">
                            <button class="btn btn-sm btn-danger" onclick="unloadModel('${instance.instance_id}')">
                                <i class="bi bi-stop"></i> 卸载
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.innerHTML += instanceCard;
    });
}

// 注册模型
async function registerModel() {
    const framework = document.getElementById('model-framework').value;

    let formData;

    if (framework === 'ollama') {
        // Ollama模型快速注册
        const ollamaModelName = document.getElementById('ollama-model-name').value;
        if (!ollamaModelName) {
            showAlert('请输入Ollama模型名称', 'warning');
            return;
        }

        formData = {
            framework: 'ollama',
            ollama_model_name: ollamaModelName,
            memory_requirement: parseInt(document.getElementById('model-memory').value),
            compute_requirement: parseFloat(document.getElementById('model-compute').value),
            description: document.getElementById('model-description').value
        };
    } else {
        // 常规模型注册
        formData = {
            name: document.getElementById('model-name').value,
            version: document.getElementById('model-version').value,
            model_type: document.getElementById('model-type').value,
            framework: framework,
            model_path: document.getElementById('model-path').value,
            memory_requirement: parseInt(document.getElementById('model-memory').value),
            compute_requirement: parseFloat(document.getElementById('model-compute').value),
            description: document.getElementById('model-description').value
        };
    }

    try {
        const response = await fetch('/api/models/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            showAlert(result.message || '模型注册成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('registerModelModal')).hide();
            document.getElementById('registerModelForm').reset();
            toggleOllamaFields(); // 重置字段显示
            loadModelList();
            loadModelOptions();
        } else {
            showAlert('模型注册失败: ' + result.error, 'danger');
        }
    } catch (error) {
        showAlert('网络错误: ' + error.message, 'danger');
    }
}

// 加载模型到vGPU
async function loadModel() {
    const modelId = document.getElementById('load-model-select').value;
    const vgpuId = document.getElementById('load-vgpu-select').value;
    const port = document.getElementById('load-model-port').value;

    if (!modelId || !vgpuId) {
        showAlert('请选择模型和vGPU', 'warning');
        return;
    }

    const formData = {
        vgpu_id: vgpuId
    };

    if (port) {
        formData.port = parseInt(port);
    }

    try {
        const response = await fetch(`/api/models/${modelId}/load`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            showAlert('模型加载请求已提交', 'success');
            bootstrap.Modal.getInstance(document.getElementById('loadModelModal')).hide();
            document.getElementById('loadModelForm').reset();
            loadModelInstances();
        } else {
            showAlert('模型加载失败: ' + result.error, 'danger');
        }
    } catch (error) {
        showAlert('网络错误: ' + error.message, 'danger');
    }
}

// 卸载模型
async function unloadModel(instanceId) {
    if (!confirm('确定要卸载这个模型实例吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/models/instances/${instanceId}/unload`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            showAlert('模型卸载成功', 'success');
            loadModelInstances();
        } else {
            showAlert('模型卸载失败: ' + result.message, 'danger');
        }
    } catch (error) {
        showAlert('网络错误: ' + error.message, 'danger');
    }
}

// 加载任务列表
async function loadTaskList() {
    try {
        const response = await fetch('/api/scheduler/tasks');
        const result = await response.json();

        if (result.success) {
            displayTaskList(result.data);
            updateTaskCounts(result.data);
        }
    } catch (error) {
        showAlert('加载任务列表失败: ' + error.message, 'danger');
    }
}

// 显示任务列表
function displayTaskList(taskData) {
    const container = document.getElementById('task-list');

    // 合并所有任务
    const allTasks = [
        ...(taskData.queue_details || []),
        ...(taskData.running_details || [])
    ];

    if (allTasks.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无任务</p>';
        return;
    }

    const table = `
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>任务ID</th>
                    <th>类型</th>
                    <th>用户</th>
                    <th>优先级</th>
                    <th>状态</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${allTasks.map(task => {
                    const statusColor = {
                        'pending': 'warning',
                        'scheduled': 'info',
                        'running': 'success',
                        'completed': 'secondary',
                        'failed': 'danger',
                        'cancelled': 'dark'
                    }[task.status] || 'secondary';

                    return `
                        <tr>
                            <td><small>${task.task_id.substring(0, 8)}...</small></td>
                            <td>${task.task_type}</td>
                            <td>${task.user_id}</td>
                            <td><span class="badge bg-primary">${task.priority}</span></td>
                            <td><span class="badge bg-${statusColor}">${task.status}</span></td>
                            <td><small>${new Date(task.created_at).toLocaleString()}</small></td>
                            <td>
                                ${task.status === 'pending' || task.status === 'running' ?
                                    `<button class="btn btn-sm btn-danger" onclick="cancelTask('${task.task_id}')">
                                        <i class="bi bi-x"></i> 取消
                                    </button>` :
                                    '<span class="text-muted">-</span>'
                                }
                            </td>
                        </tr>
                    `;
                }).join('')}
            </tbody>
        </table>
    `;

    container.innerHTML = table;
}

// 更新任务计数
function updateTaskCounts(taskData) {
    document.getElementById('pending-tasks').textContent = taskData.pending_tasks || 0;
    document.getElementById('running-tasks').textContent = taskData.running_tasks || 0;
    document.getElementById('completed-tasks').textContent = taskData.completed_tasks || 0;
}

// 提交任务
async function submitTask() {
    const formData = {
        task_type: document.getElementById('task-type').value,
        priority: parseInt(document.getElementById('task-priority').value),
        resource_requirements: {
            memory_mb: parseInt(document.getElementById('task-memory').value)
        },
        estimated_duration: parseInt(document.getElementById('task-duration').value),
        user_id: 'web_user'
    };

    try {
        const response = await fetch('/api/scheduler/submit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            showAlert('任务提交成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('submitTaskModal')).hide();
            document.getElementById('submitTaskForm').reset();
            loadTaskList();
        } else {
            showAlert('任务提交失败: ' + result.error, 'danger');
        }
    } catch (error) {
        showAlert('网络错误: ' + error.message, 'danger');
    }
}

// 取消任务
async function cancelTask(taskId) {
    if (!confirm('确定要取消这个任务吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/scheduler/task/${taskId}/cancel`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            showAlert('任务取消成功', 'success');
            loadTaskList();
        } else {
            showAlert('任务取消失败: ' + result.message, 'danger');
        }
    } catch (error) {
        showAlert('网络错误: ' + error.message, 'danger');
    }
}

// 直接加载模型到vGPU (从模型列表调用)
function loadModelToVGPU(modelId) {
    // 设置模型选择
    document.getElementById('load-model-select').value = modelId;

    // 显示加载模型模态框
    const modal = new bootstrap.Modal(document.getElementById('loadModelModal'));
    modal.show();
}

// 切换Ollama字段显示
function toggleOllamaFields() {
    const framework = document.getElementById('model-framework').value;
    const ollamaFields = document.getElementById('ollama-fields');
    const modelPathField = document.getElementById('model-path-field');
    const modelNameField = document.getElementById('model-name');
    const modelVersionField = document.getElementById('model-version');
    const modelTypeField = document.getElementById('model-type');

    if (framework === 'ollama') {
        ollamaFields.style.display = 'block';
        modelPathField.style.display = 'none';

        // Ollama模型不需要这些字段
        modelNameField.removeAttribute('required');
        modelTypeField.removeAttribute('required');
        document.getElementById('model-path').removeAttribute('required');

        // 设置默认值
        document.getElementById('model-memory').value = '4096';
        document.getElementById('model-compute').value = '50';
    } else {
        ollamaFields.style.display = 'none';
        modelPathField.style.display = 'block';

        // 恢复必需字段
        modelNameField.setAttribute('required', '');
        modelTypeField.setAttribute('required', '');
        document.getElementById('model-path').setAttribute('required', '');

        // 恢复默认值
        document.getElementById('model-memory').value = '1024';
        document.getElementById('model-compute').value = '25';
    }
}

// 显示Ollama模型浏览器
async function showOllamaModels() {
    try {
        const response = await fetch('/api/models/ollama/available');
        const result = await response.json();

        if (result.success) {
            const installedList = document.getElementById('installed-models-list');
            const popularList = document.getElementById('popular-models-list');

            // 显示已安装的模型
            if (result.data.installed_models.length > 0) {
                installedList.innerHTML = result.data.installed_models.map(model => `
                    <a href="#" class="list-group-item list-group-item-action"
                       onclick="selectOllamaModel('${model.name}')">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">${model.name}</h6>
                            <small class="text-muted">${model.size}</small>
                        </div>
                        <small class="text-muted">ID: ${model.id}</small>
                    </a>
                `).join('');
            } else {
                installedList.innerHTML = '<p class="text-muted">暂无已安装的模型</p>';
            }

            // 显示热门模型
            popularList.innerHTML = result.data.popular_models.map(model => `
                <a href="#" class="list-group-item list-group-item-action"
                   onclick="selectOllamaModel('${model}')">
                    <h6 class="mb-1">${model}</h6>
                    <small class="text-muted">点击选择此模型</small>
                </a>
            `).join('');

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('ollamaModelsModal'));
            modal.show();
        } else {
            showAlert('获取Ollama模型列表失败: ' + result.error, 'danger');
        }
    } catch (error) {
        showAlert('网络错误: ' + error.message, 'danger');
    }
}

// 选择Ollama模型
function selectOllamaModel(modelName) {
    document.getElementById('ollama-model-name').value = modelName;
    bootstrap.Modal.getInstance(document.getElementById('ollamaModelsModal')).hide();
}