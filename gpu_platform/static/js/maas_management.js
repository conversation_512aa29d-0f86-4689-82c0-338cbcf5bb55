/**
 * MAAS服务管理JavaScript
 */

// 全局变量
let currentSection = 'overview';
let apiKeys = [];
let models = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    showSection('overview');
    refreshOverview();
});

// 显示指定部分
function showSection(sectionName) {
    // 隐藏所有部分
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
        section.style.display = 'none';
    });
    
    // 显示指定部分
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.style.display = 'block';
    }
    
    // 更新导航状态
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    const activeLink = document.querySelector(`a[href="#${sectionName}"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
    
    currentSection = sectionName;
    
    // 根据部分加载相应数据
    switch(sectionName) {
        case 'overview':
            refreshOverview();
            break;
        case 'api-keys':
            refreshApiKeys();
            break;
        case 'models':
            refreshModels();
            break;
        case 'usage':
            refreshUsage();
            break;
    }
}

// 刷新概览数据
async function refreshOverview() {
    try {
        const response = await fetch('/api/maas/stats/overview');
        const data = await response.json();
        
        if (data.success) {
            const stats = data.data;
            
            document.getElementById('total-keys').textContent = stats.api_keys.total;
            document.getElementById('active-keys').textContent = stats.api_keys.active;
            document.getElementById('api-models').textContent = stats.models.api_enabled;
            document.getElementById('total-requests').textContent = stats.usage_30_days.total_requests.toLocaleString();
            document.getElementById('total-cost').textContent = '$' + stats.usage_30_days.total_cost.toFixed(2);
        }
    } catch (error) {
        console.error('刷新概览数据失败:', error);
    }
}

// 刷新API密钥列表
async function refreshApiKeys() {
    try {
        const response = await fetch('/api/maas/keys');
        const data = await response.json();
        
        if (data.success) {
            apiKeys = data.data;
            renderApiKeys();
        }
    } catch (error) {
        console.error('刷新API密钥失败:', error);
    }
}

// 渲染API密钥列表
function renderApiKeys() {
    const container = document.getElementById('api-keys-list');
    
    if (apiKeys.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-key" style="font-size: 3rem; color: #ccc;"></i>
                <h5 class="mt-3 text-muted">暂无API密钥</h5>
                <p class="text-muted">点击"创建密钥"按钮创建第一个API密钥</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    apiKeys.forEach(key => {
        const statusClass = key.is_active ? 'api-key-card' : 'api-key-card api-key-inactive';
        const statusBadge = key.is_active ? 
            '<span class="badge bg-success">活跃</span>' : 
            '<span class="badge bg-danger">已禁用</span>';
            
        html += `
            <div class="card ${statusClass}">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="card-title">
                                ${key.name} ${statusBadge}
                            </h6>
                            <p class="card-text">
                                <small class="text-muted">
                                    密钥ID: ${key.key_id}<br>
                                    创建时间: ${new Date(key.created_at).toLocaleString()}<br>
                                    速率限制: ${key.rate_limit}/分钟<br>
                                    ${key.usage_limit ? `使用额度: $${key.usage_limit}` : '无使用限制'}
                                </small>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="mb-2">
                                <small class="text-muted">30天使用量</small><br>
                                <strong>${key.usage.total_requests}</strong> 请求<br>
                                <strong>$${key.usage.total_cost.toFixed(2)}</strong> 费用
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="viewKeyDetails('${key.key_id}')">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-outline-warning" onclick="editKey('${key.key_id}')">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                ${key.is_active ? 
                                    `<button class="btn btn-outline-danger" onclick="revokeKey('${key.key_id}')">
                                        <i class="bi bi-x-circle"></i>
                                    </button>` :
                                    `<button class="btn btn-outline-success" onclick="activateKey('${key.key_id}')">
                                        <i class="bi bi-check-circle"></i>
                                    </button>`
                                }
                                <button class="btn btn-outline-danger" onclick="deleteKey('${key.key_id}')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 显示创建密钥模态框
async function showCreateKeyModal() {
    // 加载可用模型
    await loadAvailableModels();
    
    // 渲染模型选择
    const modelAccessDiv = document.getElementById('modelAccess');
    let html = '<div class="form-check"><input class="form-check-input" type="checkbox" id="allModels" checked><label class="form-check-label" for="allModels">所有模型</label></div>';
    
    models.forEach(model => {
        html += `
            <div class="form-check">
                <input class="form-check-input model-checkbox" type="checkbox" id="model-${model.model_id}" disabled>
                <label class="form-check-label" for="model-${model.model_id}">
                    ${model.name} (${model.type})
                </label>
            </div>
        `;
    });
    
    modelAccessDiv.innerHTML = html;
    
    // 添加全选/取消全选逻辑
    document.getElementById('allModels').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.model-checkbox');
        checkboxes.forEach(cb => {
            cb.disabled = this.checked;
            if (this.checked) cb.checked = false;
        });
    });
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('createKeyModal'));
    modal.show();
}

// 加载可用模型
async function loadAvailableModels() {
    try {
        const response = await fetch('/api/maas/models/available');
        const data = await response.json();
        
        if (data.success) {
            models = data.data;
        }
    } catch (error) {
        console.error('加载可用模型失败:', error);
    }
}

// 创建API密钥
async function createApiKey() {
    const form = document.getElementById('createKeyForm');
    const formData = new FormData(form);
    
    const keyData = {
        name: document.getElementById('keyName').value,
        rate_limit: parseInt(document.getElementById('rateLimit').value),
        usage_limit: document.getElementById('usageLimit').value ? parseFloat(document.getElementById('usageLimit').value) : null,
        expires_days: document.getElementById('expiresDays').value ? parseInt(document.getElementById('expiresDays').value) : null,
        model_access: []
    };
    
    // 检查模型访问权限
    const allModelsChecked = document.getElementById('allModels').checked;
    if (!allModelsChecked) {
        const modelCheckboxes = document.querySelectorAll('.model-checkbox:checked');
        keyData.model_access = Array.from(modelCheckboxes).map(cb => cb.id.replace('model-', ''));
    }
    
    try {
        const response = await fetch('/api/maas/keys', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(keyData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            // 隐藏创建模态框
            bootstrap.Modal.getInstance(document.getElementById('createKeyModal')).hide();
            
            // 显示新密钥
            document.getElementById('newApiKey').value = data.data.api_key;
            const showModal = new bootstrap.Modal(document.getElementById('showKeyModal'));
            showModal.show();
            
            // 刷新列表
            refreshApiKeys();
            
            // 重置表单
            form.reset();
        } else {
            alert('创建API密钥失败: ' + data.error);
        }
    } catch (error) {
        console.error('创建API密钥失败:', error);
        alert('创建API密钥失败: ' + error.message);
    }
}

// 复制API密钥
function copyApiKey() {
    const input = document.getElementById('newApiKey');
    input.select();
    document.execCommand('copy');
    
    // 显示复制成功提示
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="bi bi-check"></i> 已复制';
    button.classList.add('btn-success');
    button.classList.remove('btn-outline-secondary');
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('btn-success');
        button.classList.add('btn-outline-secondary');
    }, 2000);
}

// 撤销API密钥
async function revokeKey(keyId) {
    if (!confirm('确定要撤销此API密钥吗？撤销后将无法使用。')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/maas/keys/${keyId}/revoke`, {
            method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.success) {
            refreshApiKeys();
        } else {
            alert('撤销失败: ' + data.error);
        }
    } catch (error) {
        console.error('撤销API密钥失败:', error);
        alert('撤销失败: ' + error.message);
    }
}

// 删除API密钥
async function deleteKey(keyId) {
    if (!confirm('确定要删除此API密钥吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/maas/keys/${keyId}`, {
            method: 'DELETE'
        });
        
        const data = await response.json();
        
        if (data.success) {
            refreshApiKeys();
        } else {
            alert('删除失败: ' + data.error);
        }
    } catch (error) {
        console.error('删除API密钥失败:', error);
        alert('删除失败: ' + error.message);
    }
}

// 刷新模型列表
async function refreshModels() {
    try {
        const response = await fetch('/api/maas/models/available');
        const data = await response.json();
        
        if (data.success) {
            models = data.data;
            renderModels();
        }
    } catch (error) {
        console.error('刷新模型列表失败:', error);
    }
}

// 渲染模型列表
function renderModels() {
    const container = document.getElementById('models-list');
    
    if (models.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-cpu" style="font-size: 3rem; color: #ccc;"></i>
                <h5 class="mt-3 text-muted">暂无模型</h5>
            </div>
        `;
        return;
    }
    
    let html = '';
    models.forEach(model => {
        const statusBadge = model.api_enabled ? 
            '<span class="badge bg-success">API已启用</span>' : 
            '<span class="badge bg-secondary">API未启用</span>';
            
        html += `
            <div class="card model-config-card mb-3">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="card-title">
                                ${model.name} ${statusBadge}
                            </h6>
                            <p class="card-text">
                                <small class="text-muted">
                                    类型: ${model.type} | 框架: ${model.framework}<br>
                                    ${model.description}
                                </small>
                            </p>
                            ${model.api_enabled ? `
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">输入Token价格 ($/1K)</label>
                                        <input type="number" class="form-control pricing-input" 
                                               value="${model.pricing?.input_tokens || 0}" 
                                               step="0.001" 
                                               onchange="updatePricing('${model.model_id}', 'input_tokens', this.value)">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">输出Token价格 ($/1K)</label>
                                        <input type="number" class="form-control pricing-input" 
                                               value="${model.pricing?.output_tokens || 0}" 
                                               step="0.001" 
                                               onchange="updatePricing('${model.model_id}', 'output_tokens', this.value)">
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group btn-group-sm">
                                <button class="btn ${model.api_enabled ? 'btn-warning' : 'btn-success'}" 
                                        onclick="toggleModelAPI('${model.model_id}', ${!model.api_enabled})">
                                    <i class="bi bi-${model.api_enabled ? 'x-circle' : 'check-circle'}"></i>
                                    ${model.api_enabled ? '禁用API' : '启用API'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 切换模型API状态
async function toggleModelAPI(modelId, enable) {
    try {
        const response = await fetch(`/api/maas/models/${modelId}/api`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                api_enabled: enable
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            refreshModels();
        } else {
            alert('更新失败: ' + data.error);
        }
    } catch (error) {
        console.error('更新模型API状态失败:', error);
        alert('更新失败: ' + error.message);
    }
}

// 更新定价
async function updatePricing(modelId, priceType, value) {
    const model = models.find(m => m.model_id === modelId);
    if (!model) return;
    
    const pricing = model.pricing || {};
    pricing[priceType] = parseFloat(value) || 0;
    
    try {
        const response = await fetch(`/api/maas/models/${modelId}/api`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                pricing: pricing
            })
        });
        
        const data = await response.json();
        
        if (!data.success) {
            alert('更新定价失败: ' + data.error);
        }
    } catch (error) {
        console.error('更新定价失败:', error);
    }
}

// 刷新使用统计
function refreshUsage() {
    // TODO: 实现使用统计图表
    const container = document.getElementById('usage-charts');
    container.innerHTML = `
        <div class="text-center py-5">
            <i class="bi bi-graph-up" style="font-size: 3rem; color: #ccc;"></i>
            <h5 class="mt-3 text-muted">使用统计图表</h5>
            <p class="text-muted">功能开发中...</p>
        </div>
    `;
}
