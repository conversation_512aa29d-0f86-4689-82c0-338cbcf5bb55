# GPU多模型管理平台

一个功能强大的GPU资源管理和多模型部署平台，支持vGPU虚拟化、智能资源调度和多种AI框架。

## 功能特性

### 🚀 核心功能
- **GPU硬件检测**: 实时监控GPU状态、温度、显存使用等
- **vGPU虚拟化**: 将物理GPU切分为多个虚拟GPU，支持资源隔离
- **多模型支持**: 支持LLM、视觉、多模态等多种AI模型
- **智能调度**: 基于优先级和资源需求的智能任务调度
- **Web管理界面**: 直观的Web界面进行系统管理

### 🔧 技术特性
- **多框架支持**: Transformers, PyTorch, vLLM, llama.cpp, TensorRT等
- **资源管理**: 显存分配、计算资源分配、带宽管理
- **负载均衡**: 智能GPU选择和负载分布
- **实时监控**: 系统资源和模型性能实时监控
- **RESTful API**: 完整的API接口支持

## 系统要求

### 硬件要求
- NVIDIA GPU (支持CUDA)
- 8GB+ 系统内存
- 50GB+ 可用磁盘空间

### 软件要求
- Linux操作系统 (推荐Ubuntu 20.04+)
- Python 3.8+
- NVIDIA驱动 (版本 >= 470.0)
- CUDA 11.0+ (可选，用于某些框架)

## 安装指南

### 1. 克隆项目
```bash
git clone <repository-url>
cd gpu_platform
```

### 2. 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt

# 或使用conda
conda env create -f environment.yml
conda activate gpu_platform
```

### 3. 验证环境
```bash
# 检查NVIDIA驱动
nvidia-smi

# 检查Python环境
python -c "import torch; print(torch.cuda.is_available())"
```

## 快速开始

### 启动平台
```bash
# 基本启动
python run.py

# 指定端口和主机
python run.py --host 0.0.0.0 --port 8080

# 调试模式
python run.py --debug

# 跳过环境检查
python run.py --skip-checks
```

### 访问Web界面
打开浏览器访问: `http://localhost:5000`

首次启动会进入初始化向导，按照提示完成系统配置。

## 使用指南

### 1. GPU管理
- 查看GPU硬件信息和实时状态
- 监控GPU温度、功耗、利用率
- 查看运行中的进程

### 2. vGPU管理
```python
# 创建vGPU
vgpu_config = {
    "name": "vgpu-1",
    "memory_mb": 4096,
    "compute_percent": 50.0,
    "bandwidth_percent": 50.0,
    "priority": 5
}
```

### 3. 模型管理
```python
# 注册模型
model_config = {
    "name": "llama-7b",
    "model_type": "llm",
    "framework": "transformers",
    "model_path": "/path/to/model",
    "memory_requirement": 8192
}

# 加载模型到vGPU
load_config = {
    "model_id": "model-id",
    "vgpu_id": "vgpu-id",
    "port": 8000
}
```

### 4. 任务调度
```python
# 提交任务
task_config = {
    "task_type": "model_load",
    "priority": 7,
    "resource_requirements": {
        "memory_mb": 4096,
        "compute_percent": 30.0
    }
}
```

## API文档

### GPU相关API
- `GET /api/gpu/list` - 获取GPU列表
- `GET /api/status` - 获取系统状态

### vGPU相关API
- `POST /api/vgpu/create` - 创建vGPU
- `GET /api/vgpu/list` - 获取vGPU列表
- `DELETE /api/vgpu/{id}/delete` - 删除vGPU

### 模型相关API
- `POST /api/models/register` - 注册模型
- `GET /api/models/list` - 获取模型列表
- `POST /api/models/{id}/load` - 加载模型
- `GET /api/models/instances` - 获取模型实例
- `POST /api/models/instances/{id}/unload` - 卸载模型

### 调度相关API
- `POST /api/scheduler/submit` - 提交任务
- `GET /api/scheduler/tasks` - 获取任务列表
- `GET /api/scheduler/task/{id}` - 获取任务状态
- `POST /api/scheduler/task/{id}/cancel` - 取消任务

## 配置说明

### 系统配置
平台支持多种配置选项，可以通过环境变量或配置文件进行设置：

```bash
# 环境变量
export GPU_PLATFORM_HOST=0.0.0.0
export GPU_PLATFORM_PORT=5000
export GPU_PLATFORM_DEBUG=false
```

### 调度策略
支持多种调度策略：
- `FIFO`: 先进先出
- `PRIORITY`: 优先级调度
- `FAIR_SHARE`: 公平共享
- `LOAD_BALANCE`: 负载均衡
- `RESOURCE_AWARE`: 资源感知

## 监控和日志

### 日志文件
- `logs/gpu_platform.log` - 主要日志
- `logs/gpu_monitor.log` - GPU监控日志
- `logs/scheduler.log` - 调度器日志

### 监控指标
- GPU利用率和温度
- 显存使用情况
- 模型加载状态
- 任务队列状态
- 系统资源使用

## 故障排除

### 常见问题

1. **GPU检测失败**
   ```bash
   # 检查NVIDIA驱动
   nvidia-smi
   
   # 检查CUDA
   nvcc --version
   ```

2. **模型加载失败**
   - 检查模型路径是否正确
   - 确认vGPU资源是否充足
   - 查看日志文件获取详细错误信息

3. **Web界面无法访问**
   - 检查防火墙设置
   - 确认端口是否被占用
   - 查看服务器日志

### 性能优化

1. **GPU资源优化**
   - 合理分配vGPU资源
   - 避免显存碎片化
   - 监控GPU温度和功耗

2. **模型优化**
   - 使用量化模型减少显存占用
   - 启用模型并行加速推理
   - 合理设置批处理大小

## 开发指南

### 项目结构
```
gpu_platform/
├── core/                 # 核心模块
│   ├── gpu_detector.py   # GPU检测
│   ├── vgpu_manager.py   # vGPU管理
│   ├── model_manager.py  # 模型管理
│   └── scheduler.py      # 资源调度
├── templates/            # Web模板
├── static/              # 静态资源
├── app.py               # Flask应用
├── run.py               # 启动脚本
└── requirements.txt     # 依赖列表
```

### 扩展开发
1. 添加新的模型框架支持
2. 实现自定义调度策略
3. 扩展监控指标
4. 开发插件系统

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至: [<EMAIL>]

---

**注意**: 这是一个实验性项目，请在生产环境中谨慎使用。
