#!/usr/bin/env python3
"""
GPU多模型管理平台演示脚本
展示平台的主要功能
"""

import requests
import json
import time
import sys
from typing import Dict, Any

class GPUPlatformDemo:
    """GPU平台演示类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def _api_call(self, method: str, endpoint: str, data: Dict = None) -> Dict[str, Any]:
        """API调用封装"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            response.raise_for_status()
            return response.json()
        
        except requests.exceptions.RequestException as e:
            print(f"API调用失败: {e}")
            return {"success": False, "error": str(e)}
    
    def check_platform_status(self):
        """检查平台状态"""
        print("=" * 60)
        print("1. 检查平台状态")
        print("=" * 60)
        
        result = self._api_call('GET', '/api/status')
        if result.get('success'):
            data = result['data']
            platform = data['platform']
            gpu = data['gpu']
            
            print(f"✅ 平台版本: {platform['version']}")
            print(f"✅ 平台状态: {'已初始化' if platform['initialized'] else '未初始化'}")
            print(f"✅ 启动时间: {platform['start_time']}")
            print(f"✅ GPU数量: {gpu['gpu_count']}")
            
            for i, gpu_info in enumerate(gpu['gpus']):
                print(f"   GPU {i}: {gpu_info['name']}")
                print(f"   显存: {gpu_info['memory_used']}/{gpu_info['memory_total']} MB")
                print(f"   利用率: {gpu_info['utilization']:.1f}%")
                print(f"   温度: {gpu_info['temperature']:.1f}°C")
                print()
        else:
            print("❌ 平台状态检查失败")
        
        return result.get('success', False)
    
    def demo_vgpu_management(self):
        """演示vGPU管理"""
        print("=" * 60)
        print("2. vGPU管理演示")
        print("=" * 60)
        
        # 创建vGPU
        print("创建vGPU...")
        vgpu_config = {
            "name": "demo-vgpu-1",
            "gpu_id": 0,
            "memory_mb": 8192,
            "compute_percent": 50.0,
            "bandwidth_percent": 50.0,
            "priority": 5,
            "user_id": "demo_user"
        }
        
        result = self._api_call('POST', '/api/vgpu/create', vgpu_config)
        if result.get('success'):
            vgpu_id = result['data']['vgpu_id']
            print(f"✅ vGPU创建成功: {vgpu_id}")
            
            # 查看vGPU列表
            print("\n查看vGPU列表...")
            result = self._api_call('GET', '/api/vgpu/list')
            if result.get('success'):
                vgpus = result['data']
                print(f"✅ 当前vGPU数量: {len(vgpus)}")
                for vgpu in vgpus.values():
                    print(f"   vGPU: {vgpu['name']}")
                    print(f"   状态: {vgpu['status']}")
                    print(f"   显存: {vgpu['memory_mb']} MB")
                    print(f"   计算资源: {vgpu['compute_percent']}%")
            
            return vgpu_id
        else:
            print("❌ vGPU创建失败")
            return None
    
    def demo_model_management(self):
        """演示模型管理"""
        print("=" * 60)
        print("3. 模型管理演示")
        print("=" * 60)
        
        # 注册模型
        print("注册演示模型...")
        model_config = {
            "name": "demo-llama-7b",
            "model_type": "llm",
            "framework": "transformers",
            "model_path": "/path/to/demo/model",
            "memory_requirement": 4096,
            "description": "演示用的Llama 7B模型",
            "tags": ["demo", "llm", "7b"]
        }
        
        result = self._api_call('POST', '/api/models/register', model_config)
        if result.get('success'):
            model_id = result['data']['model_id']
            print(f"✅ 模型注册成功: {model_id}")
            
            # 查看模型列表
            print("\n查看模型列表...")
            result = self._api_call('GET', '/api/models/list')
            if result.get('success'):
                models = result['data']
                print(f"✅ 当前模型数量: {len(models)}")
                for model in models.values():
                    print(f"   模型: {model['name']}")
                    print(f"   类型: {model['model_type']}")
                    print(f"   框架: {model['framework']}")
                    print(f"   内存需求: {model['memory_requirement']} MB")
            
            return model_id
        else:
            print("❌ 模型注册失败")
            return None
    
    def demo_task_scheduling(self):
        """演示任务调度"""
        print("=" * 60)
        print("4. 任务调度演示")
        print("=" * 60)
        
        # 提交任务
        print("提交演示任务...")
        task_config = {
            "task_type": "model_inference",
            "priority": 7,
            "resource_requirements": {
                "memory_mb": 4096,
                "compute_percent": 30.0
            },
            "estimated_duration": 300,
            "user_id": "demo_user",
            "metadata": {
                "model_name": "demo-llama-7b",
                "input_text": "Hello, world!"
            }
        }
        
        result = self._api_call('POST', '/api/scheduler/submit', task_config)
        if result.get('success'):
            task_id = result['data']['task_id']
            print(f"✅ 任务提交成功: {task_id}")
            
            # 查看任务状态
            print("\n查看任务列表...")
            result = self._api_call('GET', '/api/scheduler/tasks')
            if result.get('success'):
                tasks = result['data']
                print(f"✅ 队列中任务: {tasks['pending_tasks']}")
                print(f"✅ 运行中任务: {tasks['running_tasks']}")
                print(f"✅ 已完成任务: {tasks['completed_tasks']}")
                
                if tasks['queue_details']:
                    print("\n队列详情:")
                    for task in tasks['queue_details']:
                        print(f"   任务ID: {task['task_id'][:8]}...")
                        print(f"   类型: {task['task_type']}")
                        print(f"   优先级: {task['priority']}")
                        print(f"   状态: {task['status']}")
            
            return task_id
        else:
            print("❌ 任务提交失败")
            return None
    
    def demo_resource_monitoring(self):
        """演示资源监控"""
        print("=" * 60)
        print("5. 资源监控演示")
        print("=" * 60)
        
        print("获取实时资源状态...")
        result = self._api_call('GET', '/api/status')
        if result.get('success'):
            data = result['data']
            
            # GPU资源使用情况
            print("GPU资源使用情况:")
            vgpu_data = data['vgpu']['gpu_usage_summary']
            for gpu_id, usage in vgpu_data.items():
                print(f"   GPU {gpu_id}: {usage['name']}")
                print(f"   显存使用: {usage['memory_allocated']}/{usage['memory_total']} MB")
                print(f"   计算资源: {usage['compute_allocated']:.1f}%")
                print(f"   带宽使用: {usage['bandwidth_allocated']:.1f}%")
                print(f"   vGPU数量: {usage['vgpu_count']}")
                print()
            
            # 调度器统计
            print("调度器统计:")
            metrics = data['scheduler']['metrics']
            print(f"   总任务数: {metrics['total_tasks']}")
            print(f"   完成任务: {metrics['completed_tasks']}")
            print(f"   失败任务: {metrics['failed_tasks']}")
            print(f"   平均等待时间: {metrics['average_wait_time']:.2f}s")
            print(f"   平均执行时间: {metrics['average_execution_time']:.2f}s")
            
        else:
            print("❌ 资源监控获取失败")
    
    def cleanup_demo_resources(self, vgpu_id: str = None, model_id: str = None, task_id: str = None):
        """清理演示资源"""
        print("=" * 60)
        print("6. 清理演示资源")
        print("=" * 60)
        
        # 取消任务
        if task_id:
            print(f"取消任务: {task_id}")
            result = self._api_call('POST', f'/api/scheduler/task/{task_id}/cancel')
            if result.get('success'):
                print("✅ 任务取消成功")
            else:
                print("❌ 任务取消失败")
        
        # 删除vGPU
        if vgpu_id:
            print(f"删除vGPU: {vgpu_id}")
            result = self._api_call('DELETE', f'/api/vgpu/{vgpu_id}/delete')
            if result.get('success'):
                print("✅ vGPU删除成功")
            else:
                print("❌ vGPU删除失败")
        
        # 注意：在实际环境中，模型通常不会被删除，这里只是演示
        print("✅ 演示资源清理完成")
    
    def run_full_demo(self):
        """运行完整演示"""
        print("🚀 GPU多模型管理平台功能演示")
        print("=" * 60)
        
        # 检查平台状态
        if not self.check_platform_status():
            print("❌ 平台未就绪，演示终止")
            return
        
        time.sleep(2)
        
        # vGPU管理演示
        vgpu_id = self.demo_vgpu_management()
        time.sleep(2)
        
        # 模型管理演示
        model_id = self.demo_model_management()
        time.sleep(2)
        
        # 任务调度演示
        task_id = self.demo_task_scheduling()
        time.sleep(2)
        
        # 资源监控演示
        self.demo_resource_monitoring()
        time.sleep(2)
        
        # 清理资源
        self.cleanup_demo_resources(vgpu_id, model_id, task_id)
        
        print("=" * 60)
        print("🎉 演示完成！")
        print("您可以通过Web界面 http://127.0.0.1:5000 继续探索平台功能")
        print("=" * 60)

def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://127.0.0.1:5000"
    
    demo = GPUPlatformDemo(base_url)
    
    try:
        demo.run_full_demo()
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"演示过程中发生错误: {e}")

if __name__ == "__main__":
    main()
