<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPU多模型管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .gpu-card {
            border-left: 4px solid #007bff;
        }
        .vgpu-card {
            border-left: 4px solid #28a745;
        }
        .model-card {
            border-left: 4px solid #ffc107;
        }
        .task-card {
            border-left: 4px solid #dc3545;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-running { background-color: #28a745; }
        .status-idle { background-color: #6c757d; }
        .status-error { background-color: #dc3545; }
        .status-loading { background-color: #ffc107; }
        .progress-bar-custom {
            height: 8px;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            padding: 20px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-2 sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center mb-4">
                        <i class="bi bi-gpu-card"></i> GPU平台
                    </h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard" onclick="showSection('dashboard')">
                                <i class="bi bi-speedometer2"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#gpu-management" onclick="showSection('gpu-management')">
                                <i class="bi bi-cpu"></i> GPU管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#vgpu-management" onclick="showSection('vgpu-management')">
                                <i class="bi bi-diagram-3"></i> vGPU管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#model-management" onclick="showSection('model-management')">
                                <i class="bi bi-brain"></i> 模型管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#task-scheduler" onclick="showSection('task-scheduler')">
                                <i class="bi bi-list-task"></i> 任务调度
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#system-monitor" onclick="showSection('system-monitor')">
                                <i class="bi bi-graph-up"></i> 系统监控
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-10 ms-sm-auto main-content">
                <!-- 仪表板 -->
                <div id="dashboard" class="section">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">仪表板</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>

                    <!-- 概览卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card metric-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">物理GPU</h6>
                                            <h3 id="gpu-count">-</h3>
                                        </div>
                                        <i class="bi bi-cpu fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card metric-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">虚拟GPU</h6>
                                            <h3 id="vgpu-count">-</h3>
                                        </div>
                                        <i class="bi bi-diagram-3 fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card metric-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">加载模型</h6>
                                            <h3 id="model-count">-</h3>
                                        </div>
                                        <i class="bi bi-brain fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card metric-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">运行任务</h6>
                                            <h3 id="task-count">-</h3>
                                        </div>
                                        <i class="bi bi-list-task fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- GPU状态 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-cpu"></i> GPU状态概览</h5>
                                </div>
                                <div class="card-body">
                                    <div id="gpu-overview" class="row">
                                        <!-- GPU卡片将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- GPU管理 -->
                <div id="gpu-management" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">GPU管理</h1>
                    </div>
                    <div id="gpu-list" class="row">
                        <!-- GPU详细信息将在这里显示 -->
                    </div>
                </div>

                <!-- vGPU管理 -->
                <div id="vgpu-management" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">vGPU管理</h1>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createVGPUModal">
                            <i class="bi bi-plus"></i> 创建vGPU
                        </button>
                    </div>
                    <div id="vgpu-list" class="row">
                        <!-- vGPU列表将在这里显示 -->
                    </div>
                </div>

                <!-- 模型管理 -->
                <div id="model-management" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">模型管理</h1>
                        <div>
                            <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#registerModelModal">
                                <i class="bi bi-plus"></i> 注册模型
                            </button>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#loadModelModal">
                                <i class="bi bi-play"></i> 加载模型
                            </button>
                        </div>
                    </div>
                    
                    <!-- 模型列表 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>已注册模型</h5>
                                </div>
                                <div class="card-body">
                                    <div id="model-list" class="table-responsive">
                                        <!-- 模型列表表格 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 模型实例 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>运行中的模型实例</h5>
                                </div>
                                <div class="card-body">
                                    <div id="model-instances" class="row">
                                        <!-- 模型实例将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务调度 -->
                <div id="task-scheduler" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">任务调度</h1>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#submitTaskModal">
                            <i class="bi bi-plus"></i> 提交任务
                        </button>
                    </div>
                    
                    <!-- 队列状态 -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">等待队列</h5>
                                    <h2 id="pending-tasks" class="text-warning">-</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">运行中</h5>
                                    <h2 id="running-tasks" class="text-success">-</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">已完成</h5>
                                    <h2 id="completed-tasks" class="text-info">-</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 任务列表 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>任务列表</h5>
                                </div>
                                <div class="card-body">
                                    <div id="task-list" class="table-responsive">
                                        <!-- 任务列表表格 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统监控 -->
                <div id="system-monitor" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">系统监控</h1>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="loadSystemInfo()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>

                    <!-- 系统概览 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-cpu fs-1 text-primary"></i>
                                    <h5 class="card-title mt-2">CPU</h5>
                                    <h3 id="cpu-usage">-</h3>
                                    <small class="text-muted" id="cpu-cores">- 核心</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-memory fs-1 text-success"></i>
                                    <h5 class="card-title mt-2">内存</h5>
                                    <h3 id="memory-usage">-</h3>
                                    <small class="text-muted" id="memory-total">- GB</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-hdd fs-1 text-warning"></i>
                                    <h5 class="card-title mt-2">磁盘</h5>
                                    <h3 id="disk-usage">-</h3>
                                    <small class="text-muted" id="disk-total">- GB</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-wifi fs-1 text-info"></i>
                                    <h5 class="card-title mt-2">网络</h5>
                                    <h3 id="network-status">-</h3>
                                    <small class="text-muted" id="network-interfaces">- 接口</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细信息 -->
                    <div class="row">
                        <!-- CPU详细信息 -->
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-cpu"></i> CPU详细信息</h5>
                                </div>
                                <div class="card-body">
                                    <div id="cpu-details">
                                        <p class="text-muted">加载中...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 内存详细信息 -->
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-memory"></i> 内存详细信息</h5>
                                </div>
                                <div class="card-body">
                                    <div id="memory-details">
                                        <p class="text-muted">加载中...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 磁盘详细信息 -->
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-hdd"></i> 磁盘详细信息</h5>
                                </div>
                                <div class="card-body">
                                    <div id="disk-details">
                                        <p class="text-muted">加载中...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 网络详细信息 -->
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-wifi"></i> 网络详细信息</h5>
                                </div>
                                <div class="card-body">
                                    <div id="network-details">
                                        <p class="text-muted">加载中...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统信息 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="bi bi-info-circle"></i> 系统信息</h5>
                                </div>
                                <div class="card-body">
                                    <div id="system-details">
                                        <p class="text-muted">加载中...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 刷新按钮 -->
    <button class="btn btn-primary btn-lg refresh-btn" onclick="refreshData()">
        <i class="bi bi-arrow-clockwise"></i>
    </button>

    <!-- 模态框 -->
    <!-- 创建vGPU模态框 -->
    <div class="modal fade" id="createVGPUModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建vGPU</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createVGPUForm">
                        <div class="mb-3">
                            <label for="vgpu-name" class="form-label">名称</label>
                            <input type="text" class="form-control" id="vgpu-name" required>
                        </div>
                        <div class="mb-3">
                            <label for="vgpu-memory" class="form-label">显存 (MB)</label>
                            <input type="number" class="form-control" id="vgpu-memory" value="2048" required>
                        </div>
                        <div class="mb-3">
                            <label for="vgpu-compute" class="form-label">计算资源 (%)</label>
                            <input type="number" class="form-control" id="vgpu-compute" value="25" min="1" max="100" required>
                        </div>
                        <div class="mb-3">
                            <label for="vgpu-bandwidth" class="form-label">带宽 (%)</label>
                            <input type="number" class="form-control" id="vgpu-bandwidth" value="25" min="1" max="100" required>
                        </div>
                        <div class="mb-3">
                            <label for="vgpu-priority" class="form-label">优先级</label>
                            <select class="form-select" id="vgpu-priority">
                                <option value="1">1 (最低)</option>
                                <option value="3">3 (低)</option>
                                <option value="5" selected>5 (中等)</option>
                                <option value="7">7 (高)</option>
                                <option value="10">10 (最高)</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createVGPU()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模型模态框 -->
    <div class="modal fade" id="registerModelModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">注册模型</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerModelForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="model-name" class="form-label">模型名称</label>
                                    <input type="text" class="form-control" id="model-name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="model-version" class="form-label">版本</label>
                                    <input type="text" class="form-control" id="model-version" value="1.0.0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="model-type" class="form-label">模型类型</label>
                                    <select class="form-select" id="model-type" required>
                                        <option value="llm">大语言模型</option>
                                        <option value="vision">视觉模型</option>
                                        <option value="multimodal">多模态模型</option>
                                        <option value="embedding">嵌入模型</option>
                                        <option value="diffusion">扩散模型</option>
                                        <option value="custom">自定义模型</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="model-framework" class="form-label">框架</label>
                                    <select class="form-select" id="model-framework" required onchange="toggleOllamaFields()">
                                        <option value="transformers">Transformers</option>
                                        <option value="pytorch">PyTorch</option>
                                        <option value="tensorflow">TensorFlow</option>
                                        <option value="onnx">ONNX</option>
                                        <option value="vllm">vLLM</option>
                                        <option value="llama.cpp">llama.cpp</option>
                                        <option value="ollama">Ollama</option>
                                        <option value="custom">自定义</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3" id="model-path-field">
                            <label for="model-path" class="form-label">模型路径</label>
                            <input type="text" class="form-control" id="model-path" required>
                        </div>

                        <!-- Ollama特定字段 -->
                        <div class="mb-3" id="ollama-fields" style="display: none;">
                            <label for="ollama-model-name" class="form-label">Ollama模型名称</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="ollama-model-name"
                                       placeholder="例如: llama2:7b, mistral:7b, codellama:13b">
                                <button class="btn btn-outline-secondary" type="button" onclick="showOllamaModels()">
                                    <i class="bi bi-list"></i> 浏览
                                </button>
                            </div>
                            <div class="form-text">
                                输入Ollama模型标识符，如 llama2:7b。点击"浏览"查看可用模型。
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="model-memory" class="form-label">内存需求 (MB)</label>
                                    <input type="number" class="form-control" id="model-memory" value="4096">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="model-compute" class="form-label">计算需求 (%)</label>
                                    <input type="number" class="form-control" id="model-compute" value="50">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="model-description" class="form-label">描述</label>
                            <textarea class="form-control" id="model-description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="registerModel()">注册</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载模型模态框 -->
    <div class="modal fade" id="loadModelModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">加载模型</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loadModelForm">
                        <div class="mb-3">
                            <label for="load-model-select" class="form-label">选择模型</label>
                            <select class="form-select" id="load-model-select" required>
                                <!-- 模型选项将动态加载 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="load-vgpu-select" class="form-label">选择vGPU</label>
                            <select class="form-select" id="load-vgpu-select" required>
                                <!-- vGPU选项将动态加载 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="load-model-port" class="form-label">端口 (可选)</label>
                            <input type="number" class="form-control" id="load-model-port" min="1024" max="65535">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="loadModel()">加载</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 提交任务模态框 -->
    <div class="modal fade" id="submitTaskModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">提交任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="submitTaskForm">
                        <div class="mb-3">
                            <label for="task-type" class="form-label">任务类型</label>
                            <select class="form-select" id="task-type" required>
                                <option value="resource_allocation">资源分配</option>
                                <option value="model_load">模型加载</option>
                                <option value="model_inference">模型推理</option>
                                <option value="model_training">模型训练</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="task-priority" class="form-label">优先级</label>
                            <select class="form-select" id="task-priority">
                                <option value="1">1 (最低)</option>
                                <option value="3">3 (低)</option>
                                <option value="5" selected>5 (中等)</option>
                                <option value="7">7 (高)</option>
                                <option value="10">10 (最高)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="task-memory" class="form-label">内存需求 (MB)</label>
                            <input type="number" class="form-control" id="task-memory" value="1024">
                        </div>
                        <div class="mb-3">
                            <label for="task-duration" class="form-label">预估时长 (秒)</label>
                            <input type="number" class="form-control" id="task-duration" value="60">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitTask()">提交</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Ollama模型浏览模态框 -->
    <div class="modal fade" id="ollamaModelsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ollama模型浏览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>已安装的模型</h6>
                            <div id="installed-models-list" class="list-group mb-3">
                                <!-- 已安装模型列表 -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>热门模型</h6>
                            <div id="popular-models-list" class="list-group">
                                <!-- 热门模型列表 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
