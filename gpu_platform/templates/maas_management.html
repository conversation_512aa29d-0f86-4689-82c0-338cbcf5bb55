<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MAAS服务管理 - GPU多模型管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            background-color: #f8f9fa;
            min-height: 100vh;
            padding: 0;
        }
        .main-content {
            padding: 20px;
        }
        .api-key-card {
            border-left: 4px solid #28a745;
            margin-bottom: 15px;
        }
        .api-key-inactive {
            border-left-color: #dc3545;
            opacity: 0.7;
        }
        .usage-chart {
            height: 300px;
        }
        .model-config-card {
            border-left: 4px solid #007bff;
        }
        .pricing-input {
            width: 120px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-2 sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center mb-4">
                        <i class="bi bi-cloud-arrow-up"></i> MAAS服务
                    </h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#overview" onclick="showSection('overview')">
                                <i class="bi bi-speedometer2"></i> 概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#api-keys" onclick="showSection('api-keys')">
                                <i class="bi bi-key"></i> API密钥
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#models" onclick="showSection('models')">
                                <i class="bi bi-cpu"></i> 模型配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#usage" onclick="showSection('usage')">
                                <i class="bi bi-graph-up"></i> 使用统计
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#docs" onclick="showSection('docs')">
                                <i class="bi bi-book"></i> API文档
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link" href="/">
                                <i class="bi bi-arrow-left"></i> 返回主页
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-10 ms-sm-auto main-content">
                <!-- 概览 -->
                <div id="overview" class="section">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">MAAS服务概览</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshOverview()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">API密钥</h5>
                                    <h2 class="text-primary" id="total-keys">-</h2>
                                    <small class="text-muted">总计 / <span id="active-keys">-</span> 活跃</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">API模型</h5>
                                    <h2 class="text-success" id="api-models">-</h2>
                                    <small class="text-muted">已启用API</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">30天请求</h5>
                                    <h2 class="text-info" id="total-requests">-</h2>
                                    <small class="text-muted">总请求数</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">30天费用</h5>
                                    <h2 class="text-warning" id="total-cost">-</h2>
                                    <small class="text-muted">人民币</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API密钥管理 -->
                <div id="api-keys" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">API密钥管理</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button type="button" class="btn btn-sm btn-primary" onclick="showCreateKeyModal()">
                                <i class="bi bi-plus"></i> 创建密钥
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="refreshApiKeys()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>

                    <div id="api-keys-list">
                        <!-- API密钥列表将在这里动态加载 -->
                    </div>
                </div>

                <!-- 模型配置 -->
                <div id="models" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">模型API配置</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshModels()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>

                    <div id="models-list">
                        <!-- 模型列表将在这里动态加载 -->
                    </div>
                </div>

                <!-- 使用统计 -->
                <div id="usage" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">使用统计</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <select class="form-select form-select-sm" id="usage-period" onchange="refreshUsage()">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近90天</option>
                            </select>
                        </div>
                    </div>

                    <div id="usage-charts">
                        <!-- 使用统计图表将在这里显示 -->
                    </div>
                </div>

                <!-- API文档 -->
                <div id="docs" class="section" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">API文档</h1>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>OpenAI兼容API</h5>
                                </div>
                                <div class="card-body">
                                    <h6>基础URL</h6>
                                    <code id="base-url">http://localhost:5000/v1</code>
                                    
                                    <h6 class="mt-3">认证</h6>
                                    <p>在请求头中包含API密钥：</p>
                                    <pre><code>Authorization: Bearer YOUR_API_KEY</code></pre>
                                    
                                    <h6 class="mt-3">支持的端点</h6>
                                    <ul>
                                        <li><code>GET /v1/models</code> - 列出可用模型</li>
                                        <li><code>POST /v1/chat/completions</code> - 聊天完成</li>
                                        <li><code>POST /v1/completions</code> - 文本完成</li>
                                    </ul>
                                    
                                    <h6 class="mt-3">示例请求</h6>
                                    <pre><code>curl -X POST "http://localhost:5000/v1/chat/completions" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "your-model-id",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ],
    "max_tokens": 100
  }'</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 创建API密钥模态框 -->
    <div class="modal fade" id="createKeyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建API密钥</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createKeyForm">
                        <div class="mb-3">
                            <label for="keyName" class="form-label">密钥名称</label>
                            <input type="text" class="form-control" id="keyName" required>
                        </div>
                        <div class="mb-3">
                            <label for="rateLimit" class="form-label">速率限制（每分钟）</label>
                            <input type="number" class="form-control" id="rateLimit" value="1000">
                        </div>
                        <div class="mb-3">
                            <label for="usageLimit" class="form-label">使用额度（人民币，可选）</label>
                            <input type="number" class="form-control" id="usageLimit" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label for="expiresDays" class="form-label">有效期（天，可选）</label>
                            <input type="number" class="form-control" id="expiresDays">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">模型访问权限</label>
                            <div id="modelAccess">
                                <!-- 模型选择将在这里动态加载 -->
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createApiKey()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 显示API密钥模态框 -->
    <div class="modal fade" id="showKeyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">API密钥创建成功</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <strong>重要：</strong> 请妥善保存以下API密钥，系统不会再次显示完整密钥。
                    </div>
                    <div class="mb-3">
                        <label class="form-label">API密钥</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="newApiKey" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyApiKey()">
                                <i class="bi bi-clipboard"></i> 复制
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/maas_management.js"></script>
</body>
</html>
