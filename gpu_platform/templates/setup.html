<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPU多模型管理平台 - 初始化</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .setup-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .setup-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }
        .setup-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        .step {
            display: none;
        }
        .step.active {
            display: block;
        }
        .progress-step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            color: #6c757d;
        }
        .progress-step.active {
            background-color: #667eea;
            color: white;
        }
        .progress-step.completed {
            background-color: #28a745;
            color: white;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="text-center">
                <i class="bi bi-gpu-card setup-icon"></i>
                <h2 class="mb-4">GPU多模型管理平台</h2>
                <p class="text-muted mb-4">欢迎使用GPU多模型管理平台，让我们开始初始化系统</p>
            </div>

            <!-- 进度指示器 -->
            <div class="d-flex justify-content-center mb-4">
                <div class="progress-step active" id="step-1">1</div>
                <div class="progress-step" id="step-2">2</div>
                <div class="progress-step" id="step-3">3</div>
            </div>

            <!-- 步骤1: 系统检查 -->
            <div class="step active" id="step-content-1">
                <h4 class="mb-3">步骤 1: 系统检查</h4>
                <p class="text-muted mb-4">正在检查系统环境和GPU硬件...</p>
                
                <div class="list-group">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>NVIDIA驱动检查</span>
                        <span id="nvidia-driver-status">
                            <span class="status-indicator status-warning"></span>检查中...
                        </span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>CUDA环境检查</span>
                        <span id="cuda-status">
                            <span class="status-indicator status-warning"></span>检查中...
                        </span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>GPU硬件检测</span>
                        <span id="gpu-detection-status">
                            <span class="status-indicator status-warning"></span>检查中...
                        </span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>Python依赖检查</span>
                        <span id="python-deps-status">
                            <span class="status-indicator status-warning"></span>检查中...
                        </span>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button class="btn btn-primary" onclick="startSystemCheck()" id="check-btn">
                        <i class="bi bi-play"></i> 开始检查
                    </button>
                </div>
            </div>

            <!-- 步骤2: GPU配置 -->
            <div class="step" id="step-content-2">
                <h4 class="mb-3">步骤 2: GPU配置</h4>
                <p class="text-muted mb-4">检测到的GPU硬件信息</p>
                
                <div id="gpu-info-container">
                    <!-- GPU信息将在这里显示 -->
                </div>

                <div class="text-center mt-4">
                    <button class="btn btn-secondary me-2" onclick="previousStep()">
                        <i class="bi bi-arrow-left"></i> 上一步
                    </button>
                    <button class="btn btn-primary" onclick="nextStep()">
                        下一步 <i class="bi bi-arrow-right"></i>
                    </button>
                </div>
            </div>

            <!-- 步骤3: 初始化完成 -->
            <div class="step" id="step-content-3">
                <h4 class="mb-3">步骤 3: 初始化平台</h4>
                <p class="text-muted mb-4">正在初始化GPU多模型管理平台...</p>
                
                <div class="list-group">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>启动GPU监控</span>
                        <span id="gpu-monitor-status">
                            <span class="status-indicator status-warning"></span>初始化中...
                        </span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>启动vGPU管理器</span>
                        <span id="vgpu-manager-status">
                            <span class="status-indicator status-warning"></span>初始化中...
                        </span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>启动模型管理器</span>
                        <span id="model-manager-status">
                            <span class="status-indicator status-warning"></span>初始化中...
                        </span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>启动资源调度器</span>
                        <span id="scheduler-status">
                            <span class="status-indicator status-warning"></span>初始化中...
                        </span>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button class="btn btn-secondary me-2" onclick="previousStep()">
                        <i class="bi bi-arrow-left"></i> 上一步
                    </button>
                    <button class="btn btn-primary" onclick="initializePlatform()" id="init-btn">
                        <i class="bi bi-rocket"></i> 初始化平台
                    </button>
                </div>

                <div id="completion-message" class="text-center mt-4" style="display: none;">
                    <div class="alert alert-success">
                        <h5><i class="bi bi-check-circle"></i> 初始化完成！</h5>
                        <p class="mb-0">GPU多模型管理平台已成功初始化，正在跳转到主界面...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStep = 1;
        let systemCheckPassed = false;

        // 开始系统检查
        async function startSystemCheck() {
            const checkBtn = document.getElementById('check-btn');
            checkBtn.disabled = true;
            checkBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 检查中...';

            // 模拟系统检查过程
            await checkNvidiaDriver();
            await checkCuda();
            await checkGPUDetection();
            await checkPythonDeps();

            systemCheckPassed = true;
            checkBtn.innerHTML = '<i class="bi bi-check"></i> 检查完成';
            
            setTimeout(() => {
                nextStep();
            }, 1000);
        }

        // 检查NVIDIA驱动
        async function checkNvidiaDriver() {
            await new Promise(resolve => setTimeout(resolve, 1000));
            updateStatus('nvidia-driver-status', true, 'NVIDIA驱动正常');
        }

        // 检查CUDA
        async function checkCuda() {
            await new Promise(resolve => setTimeout(resolve, 1000));
            updateStatus('cuda-status', true, 'CUDA环境正常');
        }

        // 检查GPU检测
        async function checkGPUDetection() {
            await new Promise(resolve => setTimeout(resolve, 1500));
            try {
                const response = await fetch('/api/gpu/list');
                const result = await response.json();
                
                if (result.success && result.data.length > 0) {
                    updateStatus('gpu-detection-status', true, `检测到 ${result.data.length} 个GPU`);
                    displayGPUInfo(result.data);
                } else {
                    updateStatus('gpu-detection-status', false, '未检测到GPU');
                }
            } catch (error) {
                updateStatus('gpu-detection-status', false, 'GPU检测失败');
            }
        }

        // 检查Python依赖
        async function checkPythonDeps() {
            await new Promise(resolve => setTimeout(resolve, 800));
            updateStatus('python-deps-status', true, 'Python依赖正常');
        }

        // 更新状态显示
        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            const statusClass = success ? 'status-success' : 'status-error';
            element.innerHTML = `<span class="status-indicator ${statusClass}"></span>${message}`;
        }

        // 显示GPU信息
        function displayGPUInfo(gpus) {
            const container = document.getElementById('gpu-info-container');
            
            const gpuCards = gpus.map(gpu => `
                <div class="card mb-2">
                    <div class="card-body">
                        <h6 class="card-title">GPU ${gpu.gpu_id}: ${gpu.name}</h6>
                        <p class="card-text">
                            <small class="text-muted">
                                显存: ${gpu.memory_total}MB | 
                                温度: ${gpu.temperature}°C | 
                                利用率: ${gpu.utilization}%
                            </small>
                        </p>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = gpuCards;
        }

        // 初始化平台
        async function initializePlatform() {
            const initBtn = document.getElementById('init-btn');
            initBtn.disabled = true;
            initBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 初始化中...';

            try {
                // 调用初始化API
                const response = await fetch('/api/initialize', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    // 模拟初始化过程
                    await updateInitStatus('gpu-monitor-status', true, 'GPU监控已启动');
                    await updateInitStatus('vgpu-manager-status', true, 'vGPU管理器已启动');
                    await updateInitStatus('model-manager-status', true, '模型管理器已启动');
                    await updateInitStatus('scheduler-status', true, '资源调度器已启动');

                    // 显示完成消息
                    document.getElementById('completion-message').style.display = 'block';
                    
                    // 跳转到主界面
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 3000);
                } else {
                    throw new Error(result.message || '初始化失败');
                }
            } catch (error) {
                alert('初始化失败: ' + error.message);
                initBtn.disabled = false;
                initBtn.innerHTML = '<i class="bi bi-rocket"></i> 重试初始化';
            }
        }

        // 更新初始化状态
        async function updateInitStatus(elementId, success, message) {
            await new Promise(resolve => setTimeout(resolve, 1000));
            updateStatus(elementId, success, message);
        }

        // 下一步
        function nextStep() {
            if (currentStep < 3) {
                // 隐藏当前步骤
                document.getElementById(`step-content-${currentStep}`).classList.remove('active');
                document.getElementById(`step-${currentStep}`).classList.remove('active');
                document.getElementById(`step-${currentStep}`).classList.add('completed');
                
                // 显示下一步
                currentStep++;
                document.getElementById(`step-content-${currentStep}`).classList.add('active');
                document.getElementById(`step-${currentStep}`).classList.add('active');
            }
        }

        // 上一步
        function previousStep() {
            if (currentStep > 1) {
                // 隐藏当前步骤
                document.getElementById(`step-content-${currentStep}`).classList.remove('active');
                document.getElementById(`step-${currentStep}`).classList.remove('active');
                
                // 显示上一步
                currentStep--;
                document.getElementById(`step-content-${currentStep}`).classList.add('active');
                document.getElementById(`step-${currentStep}`).classList.remove('completed');
                document.getElementById(`step-${currentStep}`).classList.add('active');
            }
        }
    </script>
</body>
</html>
