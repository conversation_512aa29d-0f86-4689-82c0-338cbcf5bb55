#!/usr/bin/env python3
"""
GPU多模型管理平台启动脚本
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import run_platform

def setup_logging(debug=False):
    """设置日志配置"""
    log_level = logging.DEBUG if debug else logging.INFO
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 创建logs目录
    logs_dir = project_root / 'logs'
    logs_dir.mkdir(exist_ok=True)
    
    # 配置日志
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=[
            logging.FileHandler(logs_dir / 'gpu_platform.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """检查依赖项"""
    required_packages = [
        'flask',
        'flask_cors',
        'torch',
        'transformers',
        'pynvml',
        'psutil'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"错误: 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装依赖:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_gpu_environment():
    """检查GPU环境"""
    try:
        import pynvml
        pynvml.nvmlInit()
        device_count = pynvml.nvmlDeviceGetCount()
        print(f"检测到 {device_count} 个NVIDIA GPU")
        return True
    except Exception as e:
        print(f"GPU环境检查失败: {e}")
        print("请确保已安装NVIDIA驱动和CUDA")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='GPU多模型管理平台')
    parser.add_argument('--host', default='0.0.0.0', help='绑定主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, help='绑定端口 (默认: 5000)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--skip-checks', action='store_true', help='跳过环境检查')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.debug)
    
    print("=" * 60)
    print("GPU多模型管理平台 v1.0.0")
    print("=" * 60)
    
    if not args.skip_checks:
        print("正在检查环境...")
        
        # 检查依赖
        if not check_dependencies():
            sys.exit(1)
        
        # 检查GPU环境
        if not check_gpu_environment():
            print("警告: GPU环境检查失败，某些功能可能不可用")
    
    print(f"启动Web服务器: http://{args.host}:{args.port}")
    print("按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    try:
        # 启动平台
        run_platform(host=args.host, port=args.port, debug=args.debug)
    except KeyboardInterrupt:
        print("\n正在关闭平台...")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
