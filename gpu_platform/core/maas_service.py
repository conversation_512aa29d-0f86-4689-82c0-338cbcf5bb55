"""
MAAS (Model as a Service) 服务模块
提供类似OpenRouter的API服务功能
"""

import uuid
import hashlib
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class APIKey:
    """API密钥配置"""
    key_id: str
    key_hash: str  # 存储哈希值，不存储明文
    name: str
    model_access: List[str]  # 可访问的模型ID列表
    rate_limit: int  # 每分钟请求限制
    usage_limit: Optional[float]  # 使用额度限制（美元）
    created_at: datetime
    expires_at: Optional[datetime]
    is_active: bool = True
    
class UsageTracker:
    """使用量跟踪器"""
    def __init__(self):
        self.usage_data = {}  # {api_key: {date: {model_id: usage}}}
        
    def record_usage(self, api_key: str, model_id: str, 
                    input_tokens: int, output_tokens: int, cost: float):
        """记录使用量"""
        today = datetime.now().date().isoformat()
        
        if api_key not in self.usage_data:
            self.usage_data[api_key] = {}
        if today not in self.usage_data[api_key]:
            self.usage_data[api_key][today] = {}
        if model_id not in self.usage_data[api_key][today]:
            self.usage_data[api_key][today][model_id] = {
                'requests': 0,
                'input_tokens': 0,
                'output_tokens': 0,
                'cost': 0.0
            }
            
        usage = self.usage_data[api_key][today][model_id]
        usage['requests'] += 1
        usage['input_tokens'] += input_tokens
        usage['output_tokens'] += output_tokens
        usage['cost'] += cost
        
    def get_usage(self, api_key: str, days: int = 30) -> Dict:
        """获取使用统计"""
        if api_key not in self.usage_data:
            return {'total_requests': 0, 'total_cost': 0.0, 'daily_usage': []}
            
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        total_requests = 0
        total_cost = 0.0
        daily_usage = []
        
        for date_str, models in self.usage_data[api_key].items():
            date = datetime.fromisoformat(date_str).date()
            if start_date <= date <= end_date:
                day_requests = sum(model['requests'] for model in models.values())
                day_cost = sum(model['cost'] for model in models.values())
                total_requests += day_requests
                total_cost += day_cost
                daily_usage.append({
                    'date': date_str,
                    'requests': day_requests,
                    'cost': day_cost,
                    'models': models
                })
                
        return {
            'total_requests': total_requests,
            'total_cost': total_cost,
            'daily_usage': daily_usage
        }

class RateLimiter:
    """速率限制器"""
    def __init__(self):
        self.requests = {}  # {api_key: [(timestamp, count)]}
        
    def check_rate_limit(self, api_key: str, limit: int) -> bool:
        """检查是否超过速率限制"""
        now = time.time()
        minute_ago = now - 60
        
        if api_key not in self.requests:
            self.requests[api_key] = []
            
        # 清理过期记录
        self.requests[api_key] = [
            (ts, count) for ts, count in self.requests[api_key] 
            if ts > minute_ago
        ]
        
        # 计算当前分钟内的请求数
        current_requests = sum(count for _, count in self.requests[api_key])
        
        if current_requests >= limit:
            return False
            
        # 记录新请求
        self.requests[api_key].append((now, 1))
        return True

class MAASService:
    """MAAS服务管理器"""
    
    def __init__(self, model_manager):
        self.model_manager = model_manager
        self.api_keys: Dict[str, APIKey] = {}
        self.usage_tracker = UsageTracker()
        self.rate_limiter = RateLimiter()
        self.base_url = "http://localhost:5000/v1"
        
    def generate_api_key(self, name: str, model_access: List[str] = None, 
                        rate_limit: int = 1000, usage_limit: float = None,
                        expires_days: int = None) -> tuple[str, str]:
        """生成API密钥"""
        # 生成密钥
        key_id = f"gmp-{uuid.uuid4().hex[:24]}"  # GPU Model Platform
        raw_key = f"{key_id}.{secrets.token_urlsafe(32)}"
        
        # 创建哈希
        key_hash = hashlib.sha256(raw_key.encode()).hexdigest()
        
        # 设置过期时间
        expires_at = None
        if expires_days:
            expires_at = datetime.now() + timedelta(days=expires_days)
            
        # 创建API密钥对象
        api_key = APIKey(
            key_id=key_id,
            key_hash=key_hash,
            name=name,
            model_access=model_access or [],
            rate_limit=rate_limit,
            usage_limit=usage_limit,
            created_at=datetime.now(),
            expires_at=expires_at
        )
        
        self.api_keys[key_id] = api_key
        logger.info(f"生成新的API密钥: {name} ({key_id})")
        
        return key_id, raw_key
        
    def validate_api_key(self, raw_key: str) -> Optional[APIKey]:
        """验证API密钥"""
        try:
            key_id = raw_key.split('.')[0]
            key_hash = hashlib.sha256(raw_key.encode()).hexdigest()
            
            if key_id not in self.api_keys:
                return None
                
            api_key = self.api_keys[key_id]
            
            # 检查哈希
            if api_key.key_hash != key_hash:
                return None
                
            # 检查是否激活
            if not api_key.is_active:
                return None
                
            # 检查是否过期
            if api_key.expires_at and datetime.now() > api_key.expires_at:
                return None
                
            return api_key
            
        except Exception as e:
            logger.warning(f"API密钥验证失败: {e}")
            return None
            
    def check_model_access(self, api_key: APIKey, model_id: str) -> bool:
        """检查模型访问权限"""
        if not api_key.model_access:  # 空列表表示可访问所有模型
            return True
        return model_id in api_key.model_access
        
    def get_available_models(self, api_key: APIKey) -> List[Dict]:
        """获取可用模型列表"""
        models = []
        for model_id, config in self.model_manager.model_configs.items():
            if not getattr(config, 'api_enabled', False):
                continue

            if not self.check_model_access(api_key, model_id):
                continue

            models.append({
                'id': model_id,
                'name': config.name,
                'description': config.description,
                'type': config.model_type.value,
                'framework': config.framework.value,
                'pricing': model.pricing,
                'max_tokens': model.max_sequence_length,
                'base_url': model.base_url or self.base_url
            })
            
        return models
        
    def calculate_cost(self, model_id: str, input_tokens: int, output_tokens: int) -> float:
        """计算使用成本"""
        if model_id not in self.model_manager.models:
            return 0.0
            
        model = self.model_manager.models[model_id]
        if not model.pricing:
            return 0.0
            
        input_cost = input_tokens * model.pricing.get('input_tokens', 0.0) / 1000
        output_cost = output_tokens * model.pricing.get('output_tokens', 0.0) / 1000
        
        return input_cost + output_cost
        
    def process_request(self, api_key: APIKey, model_id: str, 
                       input_tokens: int, output_tokens: int) -> bool:
        """处理请求并记录使用量"""
        # 检查速率限制
        if not self.rate_limiter.check_rate_limit(api_key.key_id, api_key.rate_limit):
            logger.warning(f"API密钥 {api_key.key_id} 超过速率限制")
            return False
            
        # 计算成本
        cost = self.calculate_cost(model_id, input_tokens, output_tokens)
        
        # 检查使用额度
        if api_key.usage_limit:
            current_usage = self.usage_tracker.get_usage(api_key.key_id, 30)
            if current_usage['total_cost'] + cost > api_key.usage_limit:
                logger.warning(f"API密钥 {api_key.key_id} 超过使用额度")
                return False
                
        # 记录使用量
        self.usage_tracker.record_usage(
            api_key.key_id, model_id, input_tokens, output_tokens, cost
        )
        
        return True
        
    def get_api_key_info(self, key_id: str) -> Optional[Dict]:
        """获取API密钥信息"""
        if key_id not in self.api_keys:
            return None
            
        api_key = self.api_keys[key_id]
        usage = self.usage_tracker.get_usage(key_id, 30)
        
        return {
            'key_id': key_id,
            'name': api_key.name,
            'model_access': api_key.model_access,
            'rate_limit': api_key.rate_limit,
            'usage_limit': api_key.usage_limit,
            'created_at': api_key.created_at.isoformat(),
            'expires_at': api_key.expires_at.isoformat() if api_key.expires_at else None,
            'is_active': api_key.is_active,
            'usage': usage
        }
        
    def list_api_keys(self) -> List[Dict]:
        """列出所有API密钥"""
        return [self.get_api_key_info(key_id) for key_id in self.api_keys.keys()]
        
    def revoke_api_key(self, key_id: str) -> bool:
        """撤销API密钥"""
        if key_id not in self.api_keys:
            return False
            
        self.api_keys[key_id].is_active = False
        logger.info(f"撤销API密钥: {key_id}")
        return True
        
    def delete_api_key(self, key_id: str) -> bool:
        """删除API密钥"""
        if key_id not in self.api_keys:
            return False
            
        del self.api_keys[key_id]
        logger.info(f"删除API密钥: {key_id}")
        return True
