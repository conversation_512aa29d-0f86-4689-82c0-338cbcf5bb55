"""
模型管理系统
支持多种大模型的加载、卸载、版本管理和配置
"""

import os
import json
import threading
import time
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
import logging
import subprocess
import shutil

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelType(Enum):
    """模型类型枚举"""
    LLM = "llm"                    # 大语言模型
    VISION = "vision"              # 视觉模型
    MULTIMODAL = "multimodal"      # 多模态模型
    EMBEDDING = "embedding"        # 嵌入模型
    DIFFUSION = "diffusion"        # 扩散模型
    CUSTOM = "custom"              # 自定义模型

class ModelStatus(Enum):
    """模型状态枚举"""
    DOWNLOADING = "downloading"    # 下载中
    LOADING = "loading"           # 加载中
    LOADED = "loaded"             # 已加载
    UNLOADING = "unloading"       # 卸载中
    UNLOADED = "unloaded"         # 已卸载
    ERROR = "error"               # 错误
    UPDATING = "updating"         # 更新中

class ModelFramework(Enum):
    """模型框架枚举"""
    TRANSFORMERS = "transformers"  # HuggingFace Transformers
    PYTORCH = "pytorch"           # PyTorch
    TENSORFLOW = "tensorflow"     # TensorFlow
    ONNX = "onnx"                # ONNX
    TENSORRT = "tensorrt"        # TensorRT
    VLLM = "vllm"                # vLLM
    LLAMACPP = "llama.cpp"       # llama.cpp
    OLLAMA = "ollama"            # Ollama
    CUSTOM = "custom"            # 自定义框架

@dataclass
class ModelConfig:
    """模型配置"""
    model_id: str
    name: str
    model_type: ModelType
    framework: ModelFramework
    version: str
    model_path: str
    config_path: Optional[str]
    tokenizer_path: Optional[str]
    requirements: List[str]
    memory_requirement: int      # MB
    compute_requirement: float   # GPU计算资源百分比
    max_batch_size: int
    max_sequence_length: int
    precision: str              # fp32, fp16, int8, int4
    quantization: Optional[str]  # 量化方法
    custom_params: Dict[str, Any]
    tags: Dict[str, str]
    description: str
    created_at: datetime
    updated_at: datetime

@dataclass
class ModelInstance:
    """模型实例"""
    instance_id: str
    model_id: str
    vgpu_id: str
    status: ModelStatus
    process_id: Optional[int]
    port: Optional[int]
    api_endpoint: Optional[str]
    load_time: Optional[datetime]
    last_request: Optional[datetime]
    request_count: int
    error_count: int
    memory_usage: int           # MB
    gpu_utilization: float      # %
    performance_stats: Dict[str, float]
    health_status: str

@dataclass
class ModelRepository:
    """模型仓库配置"""
    repo_id: str
    name: str
    url: str
    auth_token: Optional[str]
    local_path: str
    sync_enabled: bool
    last_sync: Optional[datetime]

class ModelManager:
    """模型管理器"""
    
    def __init__(self, models_dir: str = "./models", repos_dir: str = "./repositories"):
        self.models_dir = models_dir
        self.repos_dir = repos_dir
        self.model_configs: Dict[str, ModelConfig] = {}
        self.model_instances: Dict[str, ModelInstance] = {}
        self.repositories: Dict[str, ModelRepository] = {}
        self.lock = threading.RLock()
        self.monitoring = False
        self.monitor_thread = None
        
        self._initialize()
    
    def _initialize(self):
        """初始化模型管理器"""
        try:
            # 创建目录
            os.makedirs(self.models_dir, exist_ok=True)
            os.makedirs(self.repos_dir, exist_ok=True)
            
            # 加载已有模型配置
            self._load_model_configs()
            
            # 加载仓库配置
            self._load_repositories()
            
            logger.info(f"模型管理器初始化成功，加载了 {len(self.model_configs)} 个模型配置")
        except Exception as e:
            logger.error(f"模型管理器初始化失败: {e}")
    
    def _load_model_configs(self):
        """加载模型配置"""
        config_file = os.path.join(self.models_dir, "models.json")
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for model_data in data.get('models', []):
                        config = ModelConfig(**model_data)
                        self.model_configs[config.model_id] = config
            except Exception as e:
                logger.error(f"加载模型配置失败: {e}")
    
    def _save_model_configs(self):
        """保存模型配置"""
        config_file = os.path.join(self.models_dir, "models.json")
        try:
            data = {
                'models': [asdict(config) for config in self.model_configs.values()],
                'updated_at': datetime.now().isoformat()
            }
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
        except Exception as e:
            logger.error(f"保存模型配置失败: {e}")
    
    def _load_repositories(self):
        """加载仓库配置"""
        repo_file = os.path.join(self.repos_dir, "repositories.json")
        if os.path.exists(repo_file):
            try:
                with open(repo_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for repo_data in data.get('repositories', []):
                        repo = ModelRepository(**repo_data)
                        self.repositories[repo.repo_id] = repo
            except Exception as e:
                logger.error(f"加载仓库配置失败: {e}")
    
    def register_model(self, name: str, model_type: ModelType, framework: ModelFramework,
                      model_path: str, version: str = "1.0.0", 
                      memory_requirement: int = 1024, compute_requirement: float = 25.0,
                      max_batch_size: int = 1, max_sequence_length: int = 2048,
                      precision: str = "fp16", quantization: Optional[str] = None,
                      config_path: Optional[str] = None, tokenizer_path: Optional[str] = None,
                      requirements: List[str] = None, custom_params: Dict[str, Any] = None,
                      tags: Dict[str, str] = None, description: str = "") -> str:
        """注册新模型"""
        model_id = hashlib.md5(f"{name}_{version}_{model_path}".encode()).hexdigest()
        
        config = ModelConfig(
            model_id=model_id,
            name=name,
            model_type=model_type,
            framework=framework,
            version=version,
            model_path=model_path,
            config_path=config_path,
            tokenizer_path=tokenizer_path,
            requirements=requirements or [],
            memory_requirement=memory_requirement,
            compute_requirement=compute_requirement,
            max_batch_size=max_batch_size,
            max_sequence_length=max_sequence_length,
            precision=precision,
            quantization=quantization,
            custom_params=custom_params or {},
            tags=tags or {},
            description=description,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        with self.lock:
            self.model_configs[model_id] = config
            self._save_model_configs()
        
        logger.info(f"注册模型成功: {name} ({model_id})")
        return model_id

    def register_ollama_model(self, model_name: str, description: str = "",
                             memory_requirement: int = 4096, compute_requirement: float = 50.0,
                             tags: Dict[str, str] = None) -> str:
        """注册Ollama模型的便捷方法"""
        # 解析模型名称和版本
        if ":" in model_name:
            name_part, version = model_name.split(":", 1)
        else:
            name_part = model_name
            version = "latest"

        # 根据模型名称推断类型
        model_type = ModelType.LLM  # 默认为LLM
        if any(keyword in name_part.lower() for keyword in ["vision", "clip", "blip"]):
            model_type = ModelType.VISION
        elif any(keyword in name_part.lower() for keyword in ["embed", "sentence"]):
            model_type = ModelType.EMBEDDING

        # 根据模型大小调整内存需求
        if "7b" in model_name.lower():
            memory_requirement = max(memory_requirement, 8192)
        elif "13b" in model_name.lower():
            memory_requirement = max(memory_requirement, 16384)
        elif "30b" in model_name.lower() or "33b" in model_name.lower():
            memory_requirement = max(memory_requirement, 32768)
        elif "70b" in model_name.lower():
            memory_requirement = max(memory_requirement, 65536)

        return self.register_model(
            name=f"ollama-{name_part}",
            model_type=model_type,
            framework=ModelFramework.OLLAMA,
            model_path=model_name,  # 对于Ollama，这是模型标识符
            version=version,
            memory_requirement=memory_requirement,
            compute_requirement=compute_requirement,
            precision="fp16",  # Ollama默认使用fp16
            tags=tags or {"source": "ollama", "model_name": model_name},
            description=description or f"Ollama模型: {model_name}"
        )
    
    def load_model(self, model_id: str, vgpu_id: str, port: Optional[int] = None) -> Optional[str]:
        """加载模型到指定vGPU"""
        if model_id not in self.model_configs:
            logger.error(f"模型不存在: {model_id}")
            return None
        
        config = self.model_configs[model_id]
        instance_id = f"{model_id}_{vgpu_id}_{int(time.time())}"
        
        # 创建模型实例
        instance = ModelInstance(
            instance_id=instance_id,
            model_id=model_id,
            vgpu_id=vgpu_id,
            status=ModelStatus.LOADING,
            process_id=None,
            port=port,
            api_endpoint=None,
            load_time=None,
            last_request=None,
            request_count=0,
            error_count=0,
            memory_usage=0,
            gpu_utilization=0.0,
            performance_stats={},
            health_status="unknown"
        )
        
        with self.lock:
            self.model_instances[instance_id] = instance
        
        # 在后台线程中加载模型
        load_thread = threading.Thread(target=self._load_model_async, args=(instance_id,))
        load_thread.daemon = True
        load_thread.start()
        
        logger.info(f"开始加载模型: {config.name} 到 vGPU {vgpu_id}")
        return instance_id
    
    def _load_model_async(self, instance_id: str):
        """异步加载模型"""
        try:
            instance = self.model_instances[instance_id]
            config = self.model_configs[instance.model_id]
            
            # 根据框架选择加载方法
            if config.framework == ModelFramework.TRANSFORMERS:
                success = self._load_transformers_model(instance, config)
            elif config.framework == ModelFramework.VLLM:
                success = self._load_vllm_model(instance, config)
            elif config.framework == ModelFramework.LLAMACPP:
                success = self._load_llamacpp_model(instance, config)
            elif config.framework == ModelFramework.OLLAMA:
                success = self._load_ollama_model(instance, config)
            else:
                success = self._load_custom_model(instance, config)
            
            if success:
                instance.status = ModelStatus.LOADED
                instance.load_time = datetime.now()
                logger.info(f"模型加载成功: {instance_id}")
            else:
                instance.status = ModelStatus.ERROR
                logger.error(f"模型加载失败: {instance_id}")
                
        except Exception as e:
            logger.error(f"模型加载异常: {instance_id}, {e}")
            if instance_id in self.model_instances:
                self.model_instances[instance_id].status = ModelStatus.ERROR
    
    def _load_transformers_model(self, instance: ModelInstance, config: ModelConfig) -> bool:
        """加载Transformers模型"""
        try:
            # 这里应该实现具体的Transformers模型加载逻辑
            # 示例代码，实际需要根据具体需求实现
            cmd = [
                "python", "-m", "transformers.models.auto.modeling_auto",
                "--model_path", config.model_path,
                "--vgpu_id", instance.vgpu_id,
                "--port", str(instance.port or 8000)
            ]
            
            # 启动模型服务进程
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            instance.process_id = process.pid
            instance.api_endpoint = f"http://localhost:{instance.port or 8000}"
            
            # 等待模型加载完成（简化版本）
            time.sleep(10)
            
            return process.poll() is None
        except Exception as e:
            logger.error(f"Transformers模型加载失败: {e}")
            return False
    
    def _load_vllm_model(self, instance: ModelInstance, config: ModelConfig) -> bool:
        """加载vLLM模型"""
        try:
            # vLLM模型加载逻辑
            cmd = [
                "python", "-m", "vllm.entrypoints.api_server",
                "--model", config.model_path,
                "--gpu-memory-utilization", str(config.compute_requirement / 100.0),
                "--max-model-len", str(config.max_sequence_length),
                "--port", str(instance.port or 8000)
            ]
            
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            instance.process_id = process.pid
            instance.api_endpoint = f"http://localhost:{instance.port or 8000}"
            
            time.sleep(15)  # vLLM需要更长的加载时间
            
            return process.poll() is None
        except Exception as e:
            logger.error(f"vLLM模型加载失败: {e}")
            return False
    
    def _load_llamacpp_model(self, instance: ModelInstance, config: ModelConfig) -> bool:
        """加载llama.cpp模型"""
        try:
            # llama.cpp模型加载逻辑
            cmd = [
                "llama-server",
                "-m", config.model_path,
                "-c", str(config.max_sequence_length),
                "--port", str(instance.port or 8000),
                "--host", "0.0.0.0"
            ]
            
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            instance.process_id = process.pid
            instance.api_endpoint = f"http://localhost:{instance.port or 8000}"
            
            time.sleep(5)
            
            return process.poll() is None
        except Exception as e:
            logger.error(f"llama.cpp模型加载失败: {e}")
            return False
    
    def _load_ollama_model(self, instance: ModelInstance, config: ModelConfig) -> bool:
        """加载Ollama模型"""
        try:
            # 检查Ollama是否已安装
            import subprocess
            try:
                subprocess.run(["ollama", "--version"], check=True, capture_output=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                logger.error("Ollama未安装或不在PATH中")
                return False

            # 首先拉取模型（如果需要）
            model_name = config.model_path  # 对于Ollama，model_path是模型名称，如 "llama2:7b"

            # 检查模型是否已存在
            list_result = subprocess.run(
                ["ollama", "list"],
                capture_output=True,
                text=True
            )

            if model_name not in list_result.stdout:
                logger.info(f"正在拉取Ollama模型: {model_name}")
                pull_result = subprocess.run(
                    ["ollama", "pull", model_name],
                    capture_output=True,
                    text=True
                )
                if pull_result.returncode != 0:
                    logger.error(f"拉取Ollama模型失败: {pull_result.stderr}")
                    return False

            # 启动Ollama服务（如果未运行）
            try:
                # 检查Ollama服务是否运行
                import requests
                response = requests.get("http://localhost:11434/api/tags", timeout=5)
                if response.status_code != 200:
                    raise Exception("Ollama服务未运行")
            except:
                logger.info("启动Ollama服务...")
                # 在后台启动Ollama服务
                subprocess.Popen(
                    ["ollama", "serve"],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )
                time.sleep(5)  # 等待服务启动

            # 设置实例信息
            instance.api_endpoint = f"http://localhost:11434/api/generate"
            instance.process_id = None  # Ollama使用服务模式，没有单独的进程ID

            # 测试模型是否可用
            test_payload = {
                "model": model_name,
                "prompt": "Hello",
                "stream": False
            }

            import requests
            response = requests.post(
                instance.api_endpoint,
                json=test_payload,
                timeout=30
            )

            if response.status_code == 200:
                logger.info(f"Ollama模型加载成功: {model_name}")
                return True
            else:
                logger.error(f"Ollama模型测试失败: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"Ollama模型加载失败: {e}")
            return False

    def _load_custom_model(self, instance: ModelInstance, config: ModelConfig) -> bool:
        """加载自定义模型"""
        try:
            # 自定义模型加载逻辑
            # 这里可以根据config.custom_params中的参数来决定如何加载
            logger.info(f"加载自定义模型: {config.name}")

            # 模拟加载过程
            time.sleep(3)

            return True
        except Exception as e:
            logger.error(f"自定义模型加载失败: {e}")
            return False
    
    def unload_model(self, instance_id: str) -> bool:
        """卸载模型"""
        if instance_id not in self.model_instances:
            logger.error(f"模型实例不存在: {instance_id}")
            return False
        
        instance = self.model_instances[instance_id]
        instance.status = ModelStatus.UNLOADING
        
        try:
            # 终止模型进程
            if instance.process_id:
                try:
                    import psutil
                    process = psutil.Process(instance.process_id)
                    process.terminate()
                    process.wait(timeout=10)
                except:
                    # 强制杀死进程
                    try:
                        process.kill()
                    except:
                        pass
            
            # 清理资源
            instance.status = ModelStatus.UNLOADED
            
            with self.lock:
                del self.model_instances[instance_id]
            
            logger.info(f"模型卸载成功: {instance_id}")
            return True
            
        except Exception as e:
            logger.error(f"模型卸载失败: {instance_id}, {e}")
            instance.status = ModelStatus.ERROR
            return False
    
    def get_model_config(self, model_id: str) -> Optional[ModelConfig]:
        """获取模型配置"""
        return self.model_configs.get(model_id)
    
    def get_model_instance(self, instance_id: str) -> Optional[ModelInstance]:
        """获取模型实例"""
        return self.model_instances.get(instance_id)
    
    def list_models(self, model_type: Optional[ModelType] = None, 
                   framework: Optional[ModelFramework] = None) -> List[ModelConfig]:
        """列出模型"""
        models = list(self.model_configs.values())
        
        if model_type:
            models = [m for m in models if m.model_type == model_type]
        
        if framework:
            models = [m for m in models if m.framework == framework]
        
        return models
    
    def list_instances(self, status: Optional[ModelStatus] = None, 
                      vgpu_id: Optional[str] = None) -> List[ModelInstance]:
        """列出模型实例"""
        instances = list(self.model_instances.values())
        
        if status:
            instances = [i for i in instances if i.status == status]
        
        if vgpu_id:
            instances = [i for i in instances if i.vgpu_id == vgpu_id]
        
        return instances
    
    def update_model_config(self, model_id: str, **kwargs) -> bool:
        """更新模型配置"""
        if model_id not in self.model_configs:
            return False
        
        config = self.model_configs[model_id]
        
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        config.updated_at = datetime.now()
        
        with self.lock:
            self._save_model_configs()
        
        logger.info(f"模型配置更新成功: {model_id}")
        return True
    
    def delete_model(self, model_id: str, force: bool = False) -> bool:
        """删除模型"""
        if model_id not in self.model_configs:
            return False
        
        # 检查是否有运行中的实例
        running_instances = [i for i in self.model_instances.values() 
                           if i.model_id == model_id and i.status in [ModelStatus.LOADED, ModelStatus.LOADING]]
        
        if running_instances and not force:
            logger.warning(f"模型 {model_id} 有运行中的实例，无法删除")
            return False
        
        # 强制卸载所有实例
        if force:
            for instance in running_instances:
                self.unload_model(instance.instance_id)
        
        # 删除模型文件（可选）
        config = self.model_configs[model_id]
        if os.path.exists(config.model_path):
            try:
                if os.path.isdir(config.model_path):
                    shutil.rmtree(config.model_path)
                else:
                    os.remove(config.model_path)
            except Exception as e:
                logger.warning(f"删除模型文件失败: {e}")
        
        # 删除配置
        with self.lock:
            del self.model_configs[model_id]
            self._save_model_configs()
        
        logger.info(f"模型删除成功: {model_id}")
        return True
    
    def start_monitoring(self, interval: int = 30):
        """开始监控模型状态"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info(f"开始模型监控，间隔 {interval} 秒")
    
    def stop_monitoring(self):
        """停止监控模型状态"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        logger.info("模型监控已停止")
    
    def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.monitoring:
            try:
                self._update_instance_status()
                self._cleanup_failed_instances()
                time.sleep(interval)
            except Exception as e:
                logger.error(f"模型监控循环错误: {e}")
                time.sleep(interval)
    
    def _update_instance_status(self):
        """更新实例状态"""
        for instance_id, instance in self.model_instances.items():
            try:
                if instance.process_id:
                    try:
                        import psutil
                        process = psutil.Process(instance.process_id)
                        if not process.is_running():
                            instance.status = ModelStatus.ERROR
                        else:
                            # 更新资源使用情况
                            memory_info = process.memory_info()
                            instance.memory_usage = memory_info.rss // (1024 * 1024)  # MB
                    except:
                        instance.status = ModelStatus.ERROR
            except Exception as e:
                logger.error(f"更新实例状态失败: {instance_id}, {e}")
    
    def _cleanup_failed_instances(self):
        """清理失败的实例"""
        failed_instances = [i for i in self.model_instances.values() if i.status == ModelStatus.ERROR]
        
        for instance in failed_instances:
            logger.info(f"清理失败实例: {instance.instance_id}")
            self.unload_model(instance.instance_id)
    
    def _serialize_for_json(self, obj):
        """递归序列化对象为JSON兼容格式"""
        if isinstance(obj, dict):
            return {k: self._serialize_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._serialize_for_json(item) for item in obj]
        elif isinstance(obj, Enum):
            return obj.value
        elif hasattr(obj, '__dict__'):
            # 对于dataclass对象，转换其字典表示
            result = {}
            for key, value in obj.__dict__.items():
                result[key] = self._serialize_for_json(value)
            return result
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return obj

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        data = {
            'model_configs': self.model_configs,
            'model_instances': self.model_instances,
            'repositories': self.repositories,
            'stats': {
                'total_models': len(self.model_configs),
                'loaded_instances': len([i for i in self.model_instances.values() if i.status == ModelStatus.LOADED]),
                'total_instances': len(self.model_instances)
            },
            'timestamp': datetime.now().isoformat()
        }
        return self._serialize_for_json(data)

# 全局模型管理器实例
model_manager = ModelManager()

if __name__ == "__main__":
    # 测试代码
    manager = ModelManager()

    print("=== 注册测试模型 ===")
    model_id = manager.register_model(
        name="test-llama",
        model_type=ModelType.LLM,
        framework=ModelFramework.LLAMACPP,
        model_path="./models/llama-7b.gguf",
        memory_requirement=4096,
        compute_requirement=50.0
    )
    print(f"模型ID: {model_id}")

    print("\n=== 模型列表 ===")
    models = manager.list_models()
    for model in models:
        print(f"{model.name} ({model.model_id})")
        print(f"  类型: {model.model_type.value}")
        print(f"  框架: {model.framework.value}")
        print(f"  内存需求: {model.memory_requirement} MB")
        print(f"  计算需求: {model.compute_requirement}%")
