"""
GPU硬件检测模块
支持GPU类型识别、显存大小检测、GPU状态监控
"""

import subprocess
import json
import time
import threading
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

try:
    import pynvml
    PYNVML_AVAILABLE = True
except ImportError:
    PYNVML_AVAILABLE = False

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class GPUInfo:
    """GPU信息数据类"""
    gpu_id: int
    name: str
    memory_total: int  # MB
    memory_used: int   # MB
    memory_free: int   # MB
    utilization: float  # %
    temperature: float  # °C
    power_usage: float  # W
    power_limit: float  # W
    driver_version: str
    cuda_version: str
    pci_bus_id: str
    uuid: str
    compute_capability: Tuple[int, int]
    multi_processor_count: int
    clock_graphics: int  # MHz
    clock_memory: int    # MHz
    fan_speed: float     # %
    processes: List[Dict]
    timestamp: datetime

@dataclass
class SystemInfo:
    """系统信息数据类"""
    cpu_count: int
    cpu_usage: float
    memory_total: int  # MB
    memory_used: int   # MB
    memory_available: int  # MB
    disk_usage: Dict[str, Dict]
    network_io: Dict
    timestamp: datetime

class GPUDetector:
    """GPU检测器类"""
    
    def __init__(self):
        self.initialized = False
        self.gpu_count = 0
        self.monitoring = False
        self.monitor_thread = None
        self.callbacks = []
        self.mock_mode = False

        self._initialize()
    
    def _initialize(self):
        """初始化GPU检测器"""
        try:
            if PYNVML_AVAILABLE:
                pynvml.nvmlInit()
                self.gpu_count = pynvml.nvmlDeviceGetCount()
                self.initialized = True
                logger.info(f"GPU检测器初始化成功，检测到 {self.gpu_count} 个GPU")
            else:
                logger.warning("pynvml不可用，将使用nvidia-smi命令")
                self._check_nvidia_smi()
        except Exception as e:
            logger.error(f"GPU检测器初始化失败: {e}")
            self.initialized = False
    
    def _check_nvidia_smi(self):
        """检查nvidia-smi命令是否可用"""
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=count', '--format=csv,noheader,nounits'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.gpu_count = int(result.stdout.strip())
                self.initialized = True
                logger.info(f"通过nvidia-smi检测到 {self.gpu_count} 个GPU")
            else:
                logger.error("nvidia-smi命令执行失败")
        except Exception as e:
            logger.error(f"nvidia-smi检查失败: {e}")
            # 如果没有GPU，创建模拟GPU用于演示
            self._create_mock_gpus()

    def _create_mock_gpus(self):
        """创建模拟GPU用于演示"""
        self.gpu_count = 2  # 模拟2个GPU
        self.initialized = True
        self.mock_mode = True
        logger.info("创建模拟GPU用于演示")

    def get_gpu_info(self, gpu_id: int) -> Optional[GPUInfo]:
        """获取指定GPU的详细信息"""
        if not self.initialized:
            return None
        
        try:
            if hasattr(self, 'mock_mode') and self.mock_mode:
                return self._get_mock_gpu_info(gpu_id)
            elif PYNVML_AVAILABLE:
                return self._get_gpu_info_pynvml(gpu_id)
            else:
                return self._get_gpu_info_nvidia_smi(gpu_id)
        except Exception as e:
            logger.error(f"获取GPU {gpu_id} 信息失败: {e}")
            return None

    def _get_mock_gpu_info(self, gpu_id: int) -> GPUInfo:
        """生成模拟GPU信息用于演示"""
        import random

        mock_gpus = [
            {
                'name': 'NVIDIA GeForce RTX 4090 (Mock)',
                'memory_total': 24576,
                'compute_capability': (8, 9)
            },
            {
                'name': 'NVIDIA GeForce RTX 3080 (Mock)',
                'memory_total': 10240,
                'compute_capability': (8, 6)
            }
        ]

        if gpu_id >= len(mock_gpus):
            return None

        gpu_config = mock_gpus[gpu_id]

        # 模拟动态数据
        memory_used = random.randint(1024, gpu_config['memory_total'] // 2)
        utilization = random.uniform(10.0, 80.0)
        temperature = random.uniform(45.0, 75.0)
        power_usage = random.uniform(150.0, 350.0)

        return GPUInfo(
            gpu_id=gpu_id,
            name=gpu_config['name'],
            memory_total=gpu_config['memory_total'],
            memory_used=memory_used,
            memory_free=gpu_config['memory_total'] - memory_used,
            utilization=utilization,
            temperature=temperature,
            power_usage=power_usage,
            power_limit=450.0,
            driver_version="535.86.10 (Mock)",
            cuda_version="12.2 (Mock)",
            pci_bus_id=f"0000:0{gpu_id+1}:00.0",
            uuid=f"GPU-{gpu_id:08d}-1234-5678-9abc-def012345678",
            compute_capability=gpu_config['compute_capability'],
            multi_processor_count=128 if gpu_id == 0 else 68,
            clock_graphics=random.randint(1800, 2500),
            clock_memory=random.randint(9000, 11000),
            fan_speed=random.uniform(30.0, 70.0),
            processes=[
                {
                    'pid': 12345 + gpu_id,
                    'name': f'python{gpu_id}',
                    'memory_used': random.randint(512, 2048)
                }
            ] if random.random() > 0.5 else [],
            timestamp=datetime.now()
        )

    def _get_gpu_info_pynvml(self, gpu_id: int) -> GPUInfo:
        """使用pynvml获取GPU信息"""
        handle = pynvml.nvmlDeviceGetHandleByIndex(gpu_id)
        
        # 基本信息
        name = pynvml.nvmlDeviceGetName(handle).decode('utf-8')
        uuid = pynvml.nvmlDeviceGetUUID(handle).decode('utf-8')
        pci_info = pynvml.nvmlDeviceGetPciInfo(handle)
        pci_bus_id = f"{pci_info.domain:04x}:{pci_info.bus:02x}:{pci_info.device:02x}.{pci_info.function}"
        
        # 内存信息
        memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
        memory_total = memory_info.total // (1024 * 1024)  # 转换为MB
        memory_used = memory_info.used // (1024 * 1024)
        memory_free = memory_info.free // (1024 * 1024)
        
        # 利用率
        utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
        gpu_util = utilization.gpu
        
        # 温度
        try:
            temperature = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
        except:
            temperature = 0.0
        
        # 功耗
        try:
            power_usage = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # 转换为W
            power_limit = pynvml.nvmlDeviceGetPowerManagementLimitConstraints(handle)[1] / 1000.0
        except:
            power_usage = 0.0
            power_limit = 0.0
        
        # 版本信息
        try:
            driver_version = pynvml.nvmlSystemGetDriverVersion().decode('utf-8')
            cuda_version = pynvml.nvmlSystemGetCudaDriverVersion_v2()
            cuda_version = f"{cuda_version // 1000}.{(cuda_version % 1000) // 10}"
        except:
            driver_version = "Unknown"
            cuda_version = "Unknown"
        
        # 计算能力
        try:
            major = pynvml.nvmlDeviceGetCudaComputeCapability(handle)[0]
            minor = pynvml.nvmlDeviceGetCudaComputeCapability(handle)[1]
            compute_capability = (major, minor)
        except:
            compute_capability = (0, 0)
        
        # 多处理器数量
        try:
            multi_processor_count = pynvml.nvmlDeviceGetMultiProcessorCount(handle)
        except:
            multi_processor_count = 0
        
        # 时钟频率
        try:
            clock_graphics = pynvml.nvmlDeviceGetClockInfo(handle, pynvml.NVML_CLOCK_GRAPHICS)
            clock_memory = pynvml.nvmlDeviceGetClockInfo(handle, pynvml.NVML_CLOCK_MEM)
        except:
            clock_graphics = 0
            clock_memory = 0
        
        # 风扇速度
        try:
            fan_speed = pynvml.nvmlDeviceGetFanSpeed(handle)
        except:
            fan_speed = 0.0
        
        # 进程信息
        try:
            processes_info = pynvml.nvmlDeviceGetComputeRunningProcesses(handle)
            processes = []
            for proc in processes_info:
                try:
                    proc_name = pynvml.nvmlSystemGetProcessName(proc.pid).decode('utf-8')
                except:
                    proc_name = "Unknown"
                
                processes.append({
                    'pid': proc.pid,
                    'name': proc_name,
                    'memory_used': proc.usedGpuMemory // (1024 * 1024)  # MB
                })
        except:
            processes = []
        
        return GPUInfo(
            gpu_id=gpu_id,
            name=name,
            memory_total=memory_total,
            memory_used=memory_used,
            memory_free=memory_free,
            utilization=gpu_util,
            temperature=temperature,
            power_usage=power_usage,
            power_limit=power_limit,
            driver_version=driver_version,
            cuda_version=cuda_version,
            pci_bus_id=pci_bus_id,
            uuid=uuid,
            compute_capability=compute_capability,
            multi_processor_count=multi_processor_count,
            clock_graphics=clock_graphics,
            clock_memory=clock_memory,
            fan_speed=fan_speed,
            processes=processes,
            timestamp=datetime.now()
        )
    
    def _get_gpu_info_nvidia_smi(self, gpu_id: int) -> Optional[GPUInfo]:
        """使用nvidia-smi获取GPU信息"""
        try:
            cmd = [
                'nvidia-smi', '--query-gpu=name,memory.total,memory.used,memory.free,utilization.gpu,temperature.gpu,power.draw,power.limit,driver_version,uuid,pci.bus_id',
                '--format=csv,noheader,nounits', f'--id={gpu_id}'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                return None
            
            values = result.stdout.strip().split(', ')
            if len(values) < 11:
                return None
            
            return GPUInfo(
                gpu_id=gpu_id,
                name=values[0],
                memory_total=int(values[1]),
                memory_used=int(values[2]),
                memory_free=int(values[3]),
                utilization=float(values[4]),
                temperature=float(values[5]) if values[5] != '[Not Supported]' else 0.0,
                power_usage=float(values[6]) if values[6] != '[Not Supported]' else 0.0,
                power_limit=float(values[7]) if values[7] != '[Not Supported]' else 0.0,
                driver_version=values[8],
                cuda_version="Unknown",
                pci_bus_id=values[10],
                uuid=values[9],
                compute_capability=(0, 0),
                multi_processor_count=0,
                clock_graphics=0,
                clock_memory=0,
                fan_speed=0.0,
                processes=[],
                timestamp=datetime.now()
            )
        except Exception as e:
            logger.error(f"nvidia-smi获取GPU信息失败: {e}")
            return None
    
    def get_all_gpu_info(self) -> List[GPUInfo]:
        """获取所有GPU信息"""
        gpu_infos = []
        for i in range(self.gpu_count):
            info = self.get_gpu_info(i)
            if info:
                gpu_infos.append(info)
        return gpu_infos
    
    def get_system_info(self) -> Optional[SystemInfo]:
        """获取系统信息"""
        if not PSUTIL_AVAILABLE:
            return None
        
        try:
            # CPU信息
            cpu_count = psutil.cpu_count()
            cpu_usage = psutil.cpu_percent(interval=1)
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_total = memory.total // (1024 * 1024)  # MB
            memory_used = memory.used // (1024 * 1024)
            memory_available = memory.available // (1024 * 1024)
            
            # 磁盘信息
            disk_usage = {}
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_usage[partition.device] = {
                        'total': usage.total // (1024 * 1024 * 1024),  # GB
                        'used': usage.used // (1024 * 1024 * 1024),
                        'free': usage.free // (1024 * 1024 * 1024),
                        'percent': (usage.used / usage.total) * 100
                    }
                except:
                    continue
            
            # 网络信息
            network_io = psutil.net_io_counters()._asdict()
            
            return SystemInfo(
                cpu_count=cpu_count,
                cpu_usage=cpu_usage,
                memory_total=memory_total,
                memory_used=memory_used,
                memory_available=memory_available,
                disk_usage=disk_usage,
                network_io=network_io,
                timestamp=datetime.now()
            )
        except Exception as e:
            logger.error(f"获取系统信息失败: {e}")
            return None
    
    def start_monitoring(self, interval: int = 5):
        """开始监控GPU状态"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info(f"开始GPU监控，间隔 {interval} 秒")
    
    def stop_monitoring(self):
        """停止监控GPU状态"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        logger.info("GPU监控已停止")
    
    def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.monitoring:
            try:
                gpu_infos = self.get_all_gpu_info()
                system_info = self.get_system_info()
                
                # 调用回调函数
                for callback in self.callbacks:
                    try:
                        callback(gpu_infos, system_info)
                    except Exception as e:
                        logger.error(f"监控回调函数执行失败: {e}")
                
                time.sleep(interval)
            except Exception as e:
                logger.error(f"监控循环错误: {e}")
                time.sleep(interval)
    
    def add_monitor_callback(self, callback):
        """添加监控回调函数"""
        self.callbacks.append(callback)
    
    def remove_monitor_callback(self, callback):
        """移除监控回调函数"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        gpu_infos = self.get_all_gpu_info()
        system_info = self.get_system_info()
        
        return {
            'gpu_count': self.gpu_count,
            'gpus': [asdict(gpu) for gpu in gpu_infos],
            'system': asdict(system_info) if system_info else None,
            'timestamp': datetime.now().isoformat()
        }

# 全局GPU检测器实例
gpu_detector = GPUDetector()

if __name__ == "__main__":
    # 测试代码
    detector = GPUDetector()
    
    print("=== GPU信息 ===")
    gpu_infos = detector.get_all_gpu_info()
    for gpu in gpu_infos:
        print(f"GPU {gpu.gpu_id}: {gpu.name}")
        print(f"  显存: {gpu.memory_used}/{gpu.memory_total} MB ({gpu.memory_used/gpu.memory_total*100:.1f}%)")
        print(f"  利用率: {gpu.utilization}%")
        print(f"  温度: {gpu.temperature}°C")
        print(f"  功耗: {gpu.power_usage}/{gpu.power_limit} W")
        print()
    
    print("=== 系统信息 ===")
    system_info = detector.get_system_info()
    if system_info:
        print(f"CPU: {system_info.cpu_count} 核心, {system_info.cpu_usage}% 使用率")
        print(f"内存: {system_info.memory_used}/{system_info.memory_total} MB")
        print(f"磁盘: {system_info.disk_usage}")
