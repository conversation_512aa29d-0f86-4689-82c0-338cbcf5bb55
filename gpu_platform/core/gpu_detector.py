"""
GPU硬件检测模块
支持GPU类型识别、显存大小检测、GPU状态监控
"""

import subprocess
import json
import time
import threading
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

try:
    import pynvml
    PYNVML_AVAILABLE = True
except ImportError:
    PYNVML_AVAILABLE = False

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class GPUInfo:
    """GPU信息数据类"""
    gpu_id: int
    name: str
    memory_total: int  # MB
    memory_used: int   # MB
    memory_free: int   # MB
    utilization: float  # %
    temperature: float  # °C
    power_usage: float  # W
    power_limit: float  # W
    driver_version: str
    cuda_version: str
    pci_bus_id: str
    uuid: str
    compute_capability: Tuple[int, int]
    multi_processor_count: int
    clock_graphics: int  # MHz
    clock_memory: int    # MHz
    fan_speed: float     # %
    processes: List[Dict]
    timestamp: datetime

@dataclass
class SystemInfo:
    """系统信息数据类"""
    cpu_count: int
    cpu_usage: float
    cpu_freq: Dict  # CPU频率信息
    cpu_per_core: List[float]  # 每核心使用率
    memory_total: int  # MB
    memory_used: int   # MB
    memory_available: int  # MB
    memory_percent: float  # 内存使用百分比
    swap_total: int    # MB
    swap_used: int     # MB
    disk_usage: Dict[str, Dict]
    disk_io: Dict      # 磁盘IO统计
    network_io: Dict   # 网络IO统计
    network_interfaces: Dict[str, Dict]  # 网络接口详细信息
    boot_time: datetime  # 系统启动时间
    uptime: float      # 运行时间（秒）
    timestamp: datetime

class GPUDetector:
    """GPU检测器类"""
    
    def __init__(self):
        self.initialized = False
        self.gpu_count = 0
        self.monitoring = False
        self.monitor_thread = None
        self.callbacks = []
        self.mock_mode = False

        self._initialize()
    
    def _initialize(self):
        """初始化GPU检测器"""
        try:
            if PYNVML_AVAILABLE:
                pynvml.nvmlInit()
                self.gpu_count = pynvml.nvmlDeviceGetCount()
                self.initialized = True
                logger.info(f"GPU检测器初始化成功，检测到 {self.gpu_count} 个GPU")
            else:
                logger.warning("pynvml不可用，将使用nvidia-smi命令")
                self._check_nvidia_smi()
        except Exception as e:
            logger.error(f"GPU检测器初始化失败: {e}")
            self.initialized = False
    
    def _check_nvidia_smi(self):
        """检查nvidia-smi命令是否可用"""
        try:
            # 使用更简单的命令来获取GPU数量
            result = subprocess.run(['nvidia-smi', '-L'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                # 计算输出中GPU行的数量
                gpu_lines = [line for line in result.stdout.strip().split('\n') if line.startswith('GPU ')]
                self.gpu_count = len(gpu_lines)
                self.initialized = True
                logger.info(f"通过nvidia-smi检测到 {self.gpu_count} 个GPU")
            else:
                logger.error("nvidia-smi命令执行失败")
                self.initialized = False
        except Exception as e:
            logger.error(f"nvidia-smi检查失败: {e}")
            self.initialized = False



    def get_gpu_info(self, gpu_id: int) -> Optional[GPUInfo]:
        """获取指定GPU的详细信息"""
        if not self.initialized:
            return None

        try:
            if PYNVML_AVAILABLE:
                return self._get_gpu_info_pynvml(gpu_id)
            else:
                return self._get_gpu_info_nvidia_smi(gpu_id)
        except Exception as e:
            logger.error(f"获取GPU {gpu_id} 信息失败: {e}")
            return None



    def _get_gpu_info_pynvml(self, gpu_id: int) -> GPUInfo:
        """使用pynvml获取GPU信息"""
        handle = pynvml.nvmlDeviceGetHandleByIndex(gpu_id)
        
        # 基本信息
        name = pynvml.nvmlDeviceGetName(handle).decode('utf-8')
        uuid = pynvml.nvmlDeviceGetUUID(handle).decode('utf-8')
        pci_info = pynvml.nvmlDeviceGetPciInfo(handle)
        pci_bus_id = f"{pci_info.domain:04x}:{pci_info.bus:02x}:{pci_info.device:02x}.{pci_info.function}"
        
        # 内存信息
        memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
        memory_total = memory_info.total // (1024 * 1024)  # 转换为MB
        memory_used = memory_info.used // (1024 * 1024)
        memory_free = memory_info.free // (1024 * 1024)
        
        # 利用率
        utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
        gpu_util = utilization.gpu
        
        # 温度
        try:
            temperature = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
        except:
            temperature = 0.0
        
        # 功耗
        try:
            power_usage = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # 转换为W
            power_limit = pynvml.nvmlDeviceGetPowerManagementLimitConstraints(handle)[1] / 1000.0
        except:
            power_usage = 0.0
            power_limit = 0.0
        
        # 版本信息
        try:
            driver_version = pynvml.nvmlSystemGetDriverVersion().decode('utf-8')
            cuda_version = pynvml.nvmlSystemGetCudaDriverVersion_v2()
            cuda_version = f"{cuda_version // 1000}.{(cuda_version % 1000) // 10}"
        except:
            driver_version = "Unknown"
            cuda_version = "Unknown"
        
        # 计算能力
        try:
            major = pynvml.nvmlDeviceGetCudaComputeCapability(handle)[0]
            minor = pynvml.nvmlDeviceGetCudaComputeCapability(handle)[1]
            compute_capability = (major, minor)
        except:
            compute_capability = (0, 0)
        
        # 多处理器数量
        try:
            multi_processor_count = pynvml.nvmlDeviceGetMultiProcessorCount(handle)
        except:
            multi_processor_count = 0
        
        # 时钟频率
        try:
            clock_graphics = pynvml.nvmlDeviceGetClockInfo(handle, pynvml.NVML_CLOCK_GRAPHICS)
            clock_memory = pynvml.nvmlDeviceGetClockInfo(handle, pynvml.NVML_CLOCK_MEM)
        except:
            clock_graphics = 0
            clock_memory = 0
        
        # 风扇速度
        try:
            fan_speed = pynvml.nvmlDeviceGetFanSpeed(handle)
        except:
            fan_speed = 0.0
        
        # 进程信息
        try:
            processes_info = pynvml.nvmlDeviceGetComputeRunningProcesses(handle)
            processes = []
            for proc in processes_info:
                try:
                    proc_name = pynvml.nvmlSystemGetProcessName(proc.pid).decode('utf-8')
                except:
                    proc_name = "Unknown"
                
                processes.append({
                    'pid': proc.pid,
                    'name': proc_name,
                    'memory_used': proc.usedGpuMemory // (1024 * 1024)  # MB
                })
        except:
            processes = []
        
        return GPUInfo(
            gpu_id=gpu_id,
            name=name,
            memory_total=memory_total,
            memory_used=memory_used,
            memory_free=memory_free,
            utilization=gpu_util,
            temperature=temperature,
            power_usage=power_usage,
            power_limit=power_limit,
            driver_version=driver_version,
            cuda_version=cuda_version,
            pci_bus_id=pci_bus_id,
            uuid=uuid,
            compute_capability=compute_capability,
            multi_processor_count=multi_processor_count,
            clock_graphics=clock_graphics,
            clock_memory=clock_memory,
            fan_speed=fan_speed,
            processes=processes,
            timestamp=datetime.now()
        )
    
    def _get_cuda_version(self) -> str:
        """获取CUDA版本"""
        try:
            # 尝试从nvidia-smi获取CUDA版本
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'CUDA Version:' in line:
                        # 提取CUDA版本，格式类似: "CUDA Version: 12.2"
                        cuda_part = line.split('CUDA Version:')[1].strip()
                        cuda_version = cuda_part.split()[0]
                        return cuda_version

            # 如果nvidia-smi没有显示CUDA版本，尝试nvcc
            result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'release' in line.lower():
                        # 提取版本号，格式类似: "Cuda compilation tools, release 12.2, V12.2.140"
                        parts = line.split('release')[1].strip().split(',')[0]
                        return parts.strip()

            return "Unknown"
        except Exception as e:
            logger.debug(f"获取CUDA版本失败: {e}")
            return "Unknown"

    def _get_gpu_info_nvidia_smi(self, gpu_id: int) -> Optional[GPUInfo]:
        """使用nvidia-smi获取GPU信息"""
        try:
            cmd = [
                'nvidia-smi', '--query-gpu=name,memory.total,memory.used,memory.free,utilization.gpu,temperature.gpu,power.draw,power.limit,driver_version,uuid,pci.bus_id',
                '--format=csv,noheader,nounits', f'--id={gpu_id}'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                return None

            values = result.stdout.strip().split(', ')
            if len(values) < 11:
                return None

            # 获取CUDA版本（只在第一次调用时获取，避免重复调用）
            cuda_version = self._get_cuda_version()

            # 尝试获取计算能力和其他信息
            compute_capability = self._get_compute_capability_nvidia_smi(gpu_id)
            multi_processor_count = self._get_multiprocessor_count_nvidia_smi(gpu_id)
            clock_info = self._get_clock_info_nvidia_smi(gpu_id)
            processes = self._get_processes_nvidia_smi(gpu_id)

            return GPUInfo(
                gpu_id=gpu_id,
                name=values[0],
                memory_total=int(values[1]),
                memory_used=int(values[2]),
                memory_free=int(values[3]),
                utilization=float(values[4]),
                temperature=float(values[5]) if values[5] != '[Not Supported]' else 0.0,
                power_usage=float(values[6]) if values[6] != '[Not Supported]' else 0.0,
                power_limit=float(values[7]) if values[7] != '[Not Supported]' else 0.0,
                driver_version=values[8],
                cuda_version=cuda_version,
                pci_bus_id=values[10],
                uuid=values[9],
                compute_capability=compute_capability,
                multi_processor_count=multi_processor_count,
                clock_graphics=clock_info[0],
                clock_memory=clock_info[1],
                fan_speed=0.0,  # nvidia-smi查询风扇速度需要特殊权限
                processes=processes,
                timestamp=datetime.now()
            )
        except Exception as e:
            logger.error(f"nvidia-smi获取GPU信息失败: {e}")
            return None

    def _get_compute_capability_nvidia_smi(self, gpu_id: int) -> Tuple[int, int]:
        """通过nvidia-smi获取计算能力"""
        try:
            # 尝试获取计算能力，但nvidia-smi可能不支持直接查询
            # 根据GPU名称推断计算能力
            gpu_name_result = subprocess.run([
                'nvidia-smi', '--query-gpu=name', '--format=csv,noheader,nounits', f'--id={gpu_id}'
            ], capture_output=True, text=True, timeout=5)

            if gpu_name_result.returncode == 0:
                gpu_name = gpu_name_result.stdout.strip()
                # RTX 2080 Ti 的计算能力是 7.5
                if 'RTX 2080 Ti' in gpu_name:
                    return (7, 5)
                elif 'RTX 3080' in gpu_name:
                    return (8, 6)
                elif 'RTX 3090' in gpu_name:
                    return (8, 6)
                elif 'RTX 4080' in gpu_name:
                    return (8, 9)
                elif 'RTX 4090' in gpu_name:
                    return (8, 9)
                # 可以根据需要添加更多GPU型号

            return (0, 0)
        except:
            return (0, 0)

    def _get_multiprocessor_count_nvidia_smi(self, gpu_id: int) -> int:
        """通过nvidia-smi获取多处理器数量"""
        try:
            # nvidia-smi可能不直接支持查询SM数量，根据GPU型号推断
            gpu_name_result = subprocess.run([
                'nvidia-smi', '--query-gpu=name', '--format=csv,noheader,nounits', f'--id={gpu_id}'
            ], capture_output=True, text=True, timeout=5)

            if gpu_name_result.returncode == 0:
                gpu_name = gpu_name_result.stdout.strip()
                # RTX 2080 Ti 有 68 个SM
                if 'RTX 2080 Ti' in gpu_name:
                    return 68
                elif 'RTX 3080' in gpu_name:
                    return 68
                elif 'RTX 3090' in gpu_name:
                    return 82
                elif 'RTX 4080' in gpu_name:
                    return 76
                elif 'RTX 4090' in gpu_name:
                    return 128

            return 0
        except:
            return 0

    def _get_clock_info_nvidia_smi(self, gpu_id: int) -> Tuple[int, int]:
        """通过nvidia-smi获取时钟信息"""
        try:
            cmd = [
                'nvidia-smi', '--query-gpu=clocks.gr,clocks.mem',
                '--format=csv,noheader,nounits', f'--id={gpu_id}'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                values = result.stdout.strip().split(', ')
                if len(values) >= 2:
                    graphics_clock = int(values[0]) if values[0] != '[Not Supported]' else 0
                    memory_clock = int(values[1]) if values[1] != '[Not Supported]' else 0
                    return (graphics_clock, memory_clock)

            return (0, 0)
        except:
            return (0, 0)

    def _get_processes_nvidia_smi(self, gpu_id: int) -> List[Dict]:
        """通过nvidia-smi获取进程信息"""
        try:
            cmd = [
                'nvidia-smi', '--query-compute-apps=pid,name,used_memory',
                '--format=csv,noheader,nounits', f'--id={gpu_id}'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            if result.returncode == 0 and result.stdout.strip():
                processes = []
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.strip():
                        values = line.split(', ')
                        if len(values) >= 3:
                            try:
                                processes.append({
                                    'pid': int(values[0]),
                                    'name': values[1],
                                    'memory_used': int(values[2])
                                })
                            except ValueError:
                                continue
                return processes

            return []
        except:
            return []
    
    def get_all_gpu_info(self) -> List[GPUInfo]:
        """获取所有GPU信息"""
        gpu_infos = []
        for i in range(self.gpu_count):
            info = self.get_gpu_info(i)
            if info:
                gpu_infos.append(info)
        return gpu_infos
    
    def get_system_info(self) -> Optional[SystemInfo]:
        """获取系统信息"""
        if not PSUTIL_AVAILABLE:
            return None

        try:
            # CPU信息
            cpu_count = psutil.cpu_count()
            cpu_usage = psutil.cpu_percent(interval=1)
            cpu_per_core = psutil.cpu_percent(interval=1, percpu=True)

            # CPU频率信息
            try:
                cpu_freq_info = psutil.cpu_freq()
                cpu_freq = {
                    'current': cpu_freq_info.current if cpu_freq_info else 0,
                    'min': cpu_freq_info.min if cpu_freq_info else 0,
                    'max': cpu_freq_info.max if cpu_freq_info else 0
                }
            except:
                cpu_freq = {'current': 0, 'min': 0, 'max': 0}

            # 内存信息
            memory = psutil.virtual_memory()
            memory_total = memory.total // (1024 * 1024)  # MB
            memory_used = memory.used // (1024 * 1024)
            memory_available = memory.available // (1024 * 1024)
            memory_percent = memory.percent

            # 交换分区信息
            try:
                swap = psutil.swap_memory()
                swap_total = swap.total // (1024 * 1024)  # MB
                swap_used = swap.used // (1024 * 1024)
            except:
                swap_total = 0
                swap_used = 0

            # 磁盘信息
            disk_usage = {}
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_usage[partition.device] = {
                        'mountpoint': partition.mountpoint,
                        'fstype': partition.fstype,
                        'total': usage.total // (1024 * 1024 * 1024),  # GB
                        'used': usage.used // (1024 * 1024 * 1024),
                        'free': usage.free // (1024 * 1024 * 1024),
                        'percent': (usage.used / usage.total) * 100 if usage.total > 0 else 0
                    }
                except:
                    continue

            # 磁盘IO信息
            try:
                disk_io = psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {}
            except:
                disk_io = {}

            # 网络IO信息
            try:
                network_io = psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {}
            except:
                network_io = {}

            # 网络接口详细信息
            network_interfaces = {}
            try:
                for interface, addrs in psutil.net_if_addrs().items():
                    interface_info = {
                        'addresses': [],
                        'stats': {}
                    }

                    # 地址信息
                    for addr in addrs:
                        addr_info = {
                            'family': str(addr.family),
                            'address': addr.address,
                            'netmask': addr.netmask,
                            'broadcast': addr.broadcast
                        }
                        interface_info['addresses'].append(addr_info)

                    # 统计信息
                    try:
                        stats = psutil.net_if_stats()[interface]
                        interface_info['stats'] = {
                            'isup': stats.isup,
                            'duplex': str(stats.duplex),
                            'speed': stats.speed,
                            'mtu': stats.mtu
                        }
                    except:
                        pass

                    network_interfaces[interface] = interface_info
            except:
                pass

            # 系统启动时间和运行时间
            try:
                boot_time = datetime.fromtimestamp(psutil.boot_time())
                uptime = time.time() - psutil.boot_time()
            except:
                boot_time = datetime.now()
                uptime = 0

            return SystemInfo(
                cpu_count=cpu_count,
                cpu_usage=cpu_usage,
                cpu_freq=cpu_freq,
                cpu_per_core=cpu_per_core,
                memory_total=memory_total,
                memory_used=memory_used,
                memory_available=memory_available,
                memory_percent=memory_percent,
                swap_total=swap_total,
                swap_used=swap_used,
                disk_usage=disk_usage,
                disk_io=disk_io,
                network_io=network_io,
                network_interfaces=network_interfaces,
                boot_time=boot_time,
                uptime=uptime,
                timestamp=datetime.now()
            )
        except Exception as e:
            logger.error(f"获取系统信息失败: {e}")
            return None
    
    def start_monitoring(self, interval: int = 5):
        """开始监控GPU状态"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info(f"开始GPU监控，间隔 {interval} 秒")
    
    def stop_monitoring(self):
        """停止监控GPU状态"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        logger.info("GPU监控已停止")
    
    def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.monitoring:
            try:
                gpu_infos = self.get_all_gpu_info()
                system_info = self.get_system_info()
                
                # 调用回调函数
                for callback in self.callbacks:
                    try:
                        callback(gpu_infos, system_info)
                    except Exception as e:
                        logger.error(f"监控回调函数执行失败: {e}")
                
                time.sleep(interval)
            except Exception as e:
                logger.error(f"监控循环错误: {e}")
                time.sleep(interval)
    
    def add_monitor_callback(self, callback):
        """添加监控回调函数"""
        self.callbacks.append(callback)
    
    def remove_monitor_callback(self, callback):
        """移除监控回调函数"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        gpu_infos = self.get_all_gpu_info()
        system_info = self.get_system_info()
        
        return {
            'gpu_count': self.gpu_count,
            'gpus': [asdict(gpu) for gpu in gpu_infos],
            'system': asdict(system_info) if system_info else None,
            'timestamp': datetime.now().isoformat()
        }

# 全局GPU检测器实例
gpu_detector = GPUDetector()

if __name__ == "__main__":
    # 测试代码
    detector = GPUDetector()
    
    print("=== GPU信息 ===")
    gpu_infos = detector.get_all_gpu_info()
    for gpu in gpu_infos:
        print(f"GPU {gpu.gpu_id}: {gpu.name}")
        print(f"  显存: {gpu.memory_used}/{gpu.memory_total} MB ({gpu.memory_used/gpu.memory_total*100:.1f}%)")
        print(f"  利用率: {gpu.utilization}%")
        print(f"  温度: {gpu.temperature}°C")
        print(f"  功耗: {gpu.power_usage}/{gpu.power_limit} W")
        print()
    
    print("=== 系统信息 ===")
    system_info = detector.get_system_info()
    if system_info:
        print(f"CPU: {system_info.cpu_count} 核心, {system_info.cpu_usage}% 使用率")
        print(f"内存: {system_info.memory_used}/{system_info.memory_total} MB")
        print(f"磁盘: {system_info.disk_usage}")
