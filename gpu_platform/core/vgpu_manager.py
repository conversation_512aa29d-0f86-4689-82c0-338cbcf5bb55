"""
vGPU虚拟化管理模块
实现GPU资源切割、虚拟GPU分配和管理功能
"""

import uuid
import threading
import time
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import logging
import json

from .gpu_detector import gpu_detector, GPUInfo

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VGPUStatus(Enum):
    """vGPU状态枚举"""
    IDLE = "idle"           # 空闲
    ALLOCATED = "allocated" # 已分配
    RUNNING = "running"     # 运行中
    ERROR = "error"         # 错误
    MAINTENANCE = "maintenance"  # 维护中

class ResourceType(Enum):
    """资源类型枚举"""
    MEMORY = "memory"       # 显存
    COMPUTE = "compute"     # 计算单元
    BANDWIDTH = "bandwidth" # 带宽

@dataclass
class VGPUResource:
    """vGPU资源配置"""
    memory_mb: int          # 分配的显存大小(MB)
    compute_percent: float  # 计算资源百分比(0-100)
    bandwidth_percent: float # 带宽百分比(0-100)
    max_processes: int      # 最大进程数
    priority: int           # 优先级(1-10, 10最高)

@dataclass
class VGPUInstance:
    """vGPU实例"""
    vgpu_id: str
    name: str
    physical_gpu_id: int
    resource: VGPUResource
    status: VGPUStatus
    owner: str
    created_at: datetime
    last_used: datetime
    current_processes: List[int]
    usage_stats: Dict
    tags: Dict[str, str]

@dataclass
class AllocationRequest:
    """资源分配请求"""
    request_id: str
    requester: str
    memory_mb: int
    compute_percent: float
    bandwidth_percent: float
    max_processes: int
    priority: int
    duration_hours: Optional[int]  # 使用时长限制
    preferred_gpu: Optional[int]   # 首选GPU
    tags: Dict[str, str]
    created_at: datetime

class VGPUManager:
    """vGPU管理器"""
    
    def __init__(self):
        self.vgpu_instances: Dict[str, VGPUInstance] = {}
        self.allocation_requests: Dict[str, AllocationRequest] = {}
        self.physical_gpu_usage: Dict[int, Dict] = {}
        self.lock = threading.RLock()
        self.monitoring = False
        self.monitor_thread = None
        
        self._initialize()
    
    def _initialize(self):
        """初始化vGPU管理器"""
        try:
            # 初始化物理GPU使用情况
            for i in range(gpu_detector.gpu_count):
                self.physical_gpu_usage[i] = {
                    'memory_allocated': 0,      # 已分配显存(MB)
                    'compute_allocated': 0.0,   # 已分配计算资源(%)
                    'bandwidth_allocated': 0.0, # 已分配带宽(%)
                    'vgpu_count': 0,           # vGPU数量
                    'active_processes': 0       # 活跃进程数
                }
            
            logger.info(f"vGPU管理器初始化成功，管理 {gpu_detector.gpu_count} 个物理GPU")
        except Exception as e:
            logger.error(f"vGPU管理器初始化失败: {e}")
    
    def create_allocation_request(self, requester: str, memory_mb: int, 
                                compute_percent: float = 50.0, 
                                bandwidth_percent: float = 50.0,
                                max_processes: int = 1, priority: int = 5,
                                duration_hours: Optional[int] = None,
                                preferred_gpu: Optional[int] = None,
                                tags: Dict[str, str] = None) -> str:
        """创建资源分配请求"""
        request_id = str(uuid.uuid4())
        
        request = AllocationRequest(
            request_id=request_id,
            requester=requester,
            memory_mb=memory_mb,
            compute_percent=compute_percent,
            bandwidth_percent=bandwidth_percent,
            max_processes=max_processes,
            priority=priority,
            duration_hours=duration_hours,
            preferred_gpu=preferred_gpu,
            tags=tags or {},
            created_at=datetime.now()
        )
        
        with self.lock:
            self.allocation_requests[request_id] = request
        
        logger.info(f"创建资源分配请求: {request_id}, 请求者: {requester}")
        return request_id
    
    def allocate_vgpu(self, request_id: str, name: str = None) -> Optional[str]:
        """分配vGPU资源"""
        with self.lock:
            if request_id not in self.allocation_requests:
                logger.error(f"分配请求不存在: {request_id}")
                return None
            
            request = self.allocation_requests[request_id]
            
            # 选择最佳物理GPU
            best_gpu = self._select_best_gpu(request)
            if best_gpu is None:
                logger.warning(f"无法找到合适的GPU资源: {request_id}")
                return None
            
            # 创建vGPU实例
            vgpu_id = str(uuid.uuid4())
            vgpu_name = name or f"vGPU-{vgpu_id[:8]}"
            
            resource = VGPUResource(
                memory_mb=request.memory_mb,
                compute_percent=request.compute_percent,
                bandwidth_percent=request.bandwidth_percent,
                max_processes=request.max_processes,
                priority=request.priority
            )
            
            vgpu = VGPUInstance(
                vgpu_id=vgpu_id,
                name=vgpu_name,
                physical_gpu_id=best_gpu,
                resource=resource,
                status=VGPUStatus.ALLOCATED,
                owner=request.requester,
                created_at=datetime.now(),
                last_used=datetime.now(),
                current_processes=[],
                usage_stats={
                    'total_runtime': 0,
                    'memory_peak': 0,
                    'compute_peak': 0.0,
                    'process_count': 0
                },
                tags=request.tags
            )
            
            # 更新资源使用情况
            self.vgpu_instances[vgpu_id] = vgpu
            self._update_gpu_usage(best_gpu, request, allocate=True)
            
            # 移除分配请求
            del self.allocation_requests[request_id]
            
            logger.info(f"成功分配vGPU: {vgpu_id} 到GPU {best_gpu}")
            return vgpu_id
    
    def _select_best_gpu(self, request: AllocationRequest) -> Optional[int]:
        """选择最佳GPU"""
        best_gpu = None
        best_score = -1
        
        # 如果指定了首选GPU，优先检查
        gpu_candidates = [request.preferred_gpu] if request.preferred_gpu is not None else range(gpu_detector.gpu_count)
        
        for gpu_id in gpu_candidates:
            if gpu_id >= gpu_detector.gpu_count:
                continue
            
            # 检查资源是否足够
            if not self._check_gpu_capacity(gpu_id, request):
                continue
            
            # 计算适配度分数
            score = self._calculate_gpu_score(gpu_id, request)
            if score > best_score:
                best_score = score
                best_gpu = gpu_id
        
        return best_gpu
    
    def _check_gpu_capacity(self, gpu_id: int, request: AllocationRequest) -> bool:
        """检查GPU容量是否足够"""
        gpu_info = gpu_detector.get_gpu_info(gpu_id)
        if not gpu_info:
            return False
        
        usage = self.physical_gpu_usage[gpu_id]
        
        # 检查显存
        available_memory = gpu_info.memory_total - usage['memory_allocated']
        if available_memory < request.memory_mb:
            return False
        
        # 检查计算资源
        available_compute = 100.0 - usage['compute_allocated']
        if available_compute < request.compute_percent:
            return False
        
        # 检查带宽
        available_bandwidth = 100.0 - usage['bandwidth_allocated']
        if available_bandwidth < request.bandwidth_percent:
            return False
        
        return True
    
    def _calculate_gpu_score(self, gpu_id: int, request: AllocationRequest) -> float:
        """计算GPU适配度分数"""
        gpu_info = gpu_detector.get_gpu_info(gpu_id)
        if not gpu_info:
            return 0.0
        
        usage = self.physical_gpu_usage[gpu_id]
        
        # 基础分数：资源利用率越低分数越高
        memory_util = usage['memory_allocated'] / gpu_info.memory_total
        compute_util = usage['compute_allocated'] / 100.0
        bandwidth_util = usage['bandwidth_allocated'] / 100.0
        
        # 平衡性分数：避免某个资源过度使用
        balance_score = 1.0 - max(memory_util, compute_util, bandwidth_util)
        
        # 负载分数：vGPU数量越少分数越高
        load_score = 1.0 / (1.0 + usage['vgpu_count'])
        
        # 温度分数：温度越低分数越高
        temp_score = max(0, (85 - gpu_info.temperature) / 85.0)
        
        # 综合分数
        total_score = balance_score * 0.4 + load_score * 0.3 + temp_score * 0.3
        
        return total_score
    
    def _update_gpu_usage(self, gpu_id: int, request: AllocationRequest, allocate: bool = True):
        """更新GPU使用情况"""
        usage = self.physical_gpu_usage[gpu_id]
        multiplier = 1 if allocate else -1
        
        usage['memory_allocated'] += request.memory_mb * multiplier
        usage['compute_allocated'] += request.compute_percent * multiplier
        usage['bandwidth_allocated'] += request.bandwidth_percent * multiplier
        usage['vgpu_count'] += 1 * multiplier
    
    def deallocate_vgpu(self, vgpu_id: str) -> bool:
        """释放vGPU资源"""
        with self.lock:
            if vgpu_id not in self.vgpu_instances:
                logger.error(f"vGPU不存在: {vgpu_id}")
                return False
            
            vgpu = self.vgpu_instances[vgpu_id]
            
            # 检查是否有运行中的进程
            if vgpu.current_processes:
                logger.warning(f"vGPU {vgpu_id} 仍有运行中的进程，强制释放")
            
            # 创建临时请求对象用于更新使用情况
            temp_request = AllocationRequest(
                request_id="temp",
                requester=vgpu.owner,
                memory_mb=vgpu.resource.memory_mb,
                compute_percent=vgpu.resource.compute_percent,
                bandwidth_percent=vgpu.resource.bandwidth_percent,
                max_processes=vgpu.resource.max_processes,
                priority=vgpu.resource.priority,
                duration_hours=None,
                preferred_gpu=None,
                tags={},
                created_at=datetime.now()
            )
            
            # 更新GPU使用情况
            self._update_gpu_usage(vgpu.physical_gpu_id, temp_request, allocate=False)
            
            # 移除vGPU实例
            del self.vgpu_instances[vgpu_id]
            
            logger.info(f"成功释放vGPU: {vgpu_id}")
            return True
    
    def get_vgpu_info(self, vgpu_id: str) -> Optional[VGPUInstance]:
        """获取vGPU信息"""
        return self.vgpu_instances.get(vgpu_id)
    
    def list_vgpus(self, owner: str = None, status: VGPUStatus = None) -> List[VGPUInstance]:
        """列出vGPU实例"""
        vgpus = list(self.vgpu_instances.values())
        
        if owner:
            vgpus = [v for v in vgpus if v.owner == owner]
        
        if status:
            vgpus = [v for v in vgpus if v.status == status]
        
        return vgpus
    
    def get_gpu_usage_summary(self) -> Dict:
        """获取GPU使用情况摘要"""
        summary = {}
        
        for gpu_id in range(gpu_detector.gpu_count):
            gpu_info = gpu_detector.get_gpu_info(gpu_id)
            usage = self.physical_gpu_usage[gpu_id]
            
            if gpu_info:
                summary[gpu_id] = {
                    'name': gpu_info.name,
                    'memory_total': gpu_info.memory_total,
                    'memory_allocated': usage['memory_allocated'],
                    'memory_available': gpu_info.memory_total - usage['memory_allocated'],
                    'memory_utilization': usage['memory_allocated'] / gpu_info.memory_total * 100,
                    'compute_allocated': usage['compute_allocated'],
                    'compute_available': 100.0 - usage['compute_allocated'],
                    'bandwidth_allocated': usage['bandwidth_allocated'],
                    'bandwidth_available': 100.0 - usage['bandwidth_allocated'],
                    'vgpu_count': usage['vgpu_count'],
                    'temperature': gpu_info.temperature,
                    'power_usage': gpu_info.power_usage,
                    'utilization': gpu_info.utilization
                }
        
        return summary
    
    def resize_vgpu(self, vgpu_id: str, new_memory_mb: int = None, 
                   new_compute_percent: float = None, 
                   new_bandwidth_percent: float = None) -> bool:
        """调整vGPU资源大小"""
        with self.lock:
            if vgpu_id not in self.vgpu_instances:
                return False
            
            vgpu = self.vgpu_instances[vgpu_id]
            old_resource = vgpu.resource
            
            # 创建新的资源配置
            new_resource = VGPUResource(
                memory_mb=new_memory_mb or old_resource.memory_mb,
                compute_percent=new_compute_percent or old_resource.compute_percent,
                bandwidth_percent=new_bandwidth_percent or old_resource.bandwidth_percent,
                max_processes=old_resource.max_processes,
                priority=old_resource.priority
            )
            
            # 检查新配置是否可行
            temp_request = AllocationRequest(
                request_id="temp",
                requester=vgpu.owner,
                memory_mb=new_resource.memory_mb - old_resource.memory_mb,
                compute_percent=new_resource.compute_percent - old_resource.compute_percent,
                bandwidth_percent=new_resource.bandwidth_percent - old_resource.bandwidth_percent,
                max_processes=new_resource.max_processes,
                priority=new_resource.priority,
                duration_hours=None,
                preferred_gpu=vgpu.physical_gpu_id,
                tags={},
                created_at=datetime.now()
            )
            
            if not self._check_gpu_capacity(vgpu.physical_gpu_id, temp_request):
                logger.warning(f"vGPU {vgpu_id} 资源调整失败：容量不足")
                return False
            
            # 更新资源配置
            self._update_gpu_usage(vgpu.physical_gpu_id, temp_request, allocate=True)
            vgpu.resource = new_resource
            
            logger.info(f"vGPU {vgpu_id} 资源调整成功")
            return True
    
    def start_monitoring(self, interval: int = 10):
        """开始监控vGPU状态"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info(f"开始vGPU监控，间隔 {interval} 秒")
    
    def stop_monitoring(self):
        """停止监控vGPU状态"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        logger.info("vGPU监控已停止")
    
    def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.monitoring:
            try:
                self._update_vgpu_status()
                self._cleanup_expired_vgpus()
                time.sleep(interval)
            except Exception as e:
                logger.error(f"vGPU监控循环错误: {e}")
                time.sleep(interval)
    
    def _update_vgpu_status(self):
        """更新vGPU状态"""
        for vgpu_id, vgpu in self.vgpu_instances.items():
            try:
                # 检查进程状态
                gpu_info = gpu_detector.get_gpu_info(vgpu.physical_gpu_id)
                if gpu_info:
                    # 更新当前进程列表
                    current_pids = [proc['pid'] for proc in gpu_info.processes]
                    vgpu.current_processes = [pid for pid in vgpu.current_processes if pid in current_pids]
                    
                    # 更新状态
                    if vgpu.current_processes:
                        vgpu.status = VGPUStatus.RUNNING
                        vgpu.last_used = datetime.now()
                    elif vgpu.status == VGPUStatus.RUNNING:
                        vgpu.status = VGPUStatus.ALLOCATED
                    
                    # 更新使用统计
                    vgpu.usage_stats['process_count'] = len(vgpu.current_processes)
                    
            except Exception as e:
                logger.error(f"更新vGPU {vgpu_id} 状态失败: {e}")
                vgpu.status = VGPUStatus.ERROR
    
    def _cleanup_expired_vgpus(self):
        """清理过期的vGPU"""
        current_time = datetime.now()
        expired_vgpus = []
        
        for vgpu_id, vgpu in self.vgpu_instances.items():
            # 检查是否长时间未使用（超过24小时）
            if (current_time - vgpu.last_used).total_seconds() > 24 * 3600:
                if not vgpu.current_processes:  # 确保没有运行中的进程
                    expired_vgpus.append(vgpu_id)
        
        for vgpu_id in expired_vgpus:
            logger.info(f"清理过期vGPU: {vgpu_id}")
            self.deallocate_vgpu(vgpu_id)
    
    def _serialize_for_json(self, obj):
        """递归序列化对象为JSON兼容格式"""
        if isinstance(obj, dict):
            return {k: self._serialize_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._serialize_for_json(item) for item in obj]
        elif isinstance(obj, Enum):
            return obj.value
        elif hasattr(obj, '__dict__'):
            # 对于dataclass对象，转换其字典表示
            result = {}
            for key, value in obj.__dict__.items():
                result[key] = self._serialize_for_json(value)
            return result
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return obj

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        data = {
            'vgpu_instances': {k: asdict(v) for k, v in self.vgpu_instances.items()},
            'allocation_requests': {k: asdict(v) for k, v in self.allocation_requests.items()},
            'gpu_usage_summary': self.get_gpu_usage_summary(),
            'timestamp': datetime.now().isoformat()
        }
        return self._serialize_for_json(data)

# 全局vGPU管理器实例
vgpu_manager = VGPUManager()

if __name__ == "__main__":
    # 测试代码
    manager = VGPUManager()
    
    print("=== 创建分配请求 ===")
    request_id = manager.create_allocation_request(
        requester="test_user",
        memory_mb=2048,
        compute_percent=30.0,
        bandwidth_percent=25.0
    )
    print(f"请求ID: {request_id}")
    
    print("\n=== 分配vGPU ===")
    vgpu_id = manager.allocate_vgpu(request_id, "test-vgpu")
    if vgpu_id:
        print(f"vGPU ID: {vgpu_id}")
        
        print("\n=== GPU使用情况 ===")
        summary = manager.get_gpu_usage_summary()
        for gpu_id, info in summary.items():
            print(f"GPU {gpu_id}: {info['name']}")
            print(f"  显存: {info['memory_allocated']}/{info['memory_total']} MB")
            print(f"  计算: {info['compute_allocated']:.1f}%")
            print(f"  带宽: {info['bandwidth_allocated']:.1f}%")
            print(f"  vGPU数量: {info['vgpu_count']}")
    else:
        print("vGPU分配失败")
