"""
资源调度引擎
实现智能GPU资源分配、负载均衡和优先级调度
"""

import threading
import time
import heapq
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import logging
import uuid

from .gpu_detector import gpu_detector
from .vgpu_manager import vgpu_manager, AllocationRequest, VGPUStatus
from .model_manager import model_manager, ModelStatus

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SchedulingPolicy(Enum):
    """调度策略枚举"""
    FIFO = "fifo"                    # 先进先出
    PRIORITY = "priority"            # 优先级调度
    FAIR_SHARE = "fair_share"        # 公平共享
    LOAD_BALANCE = "load_balance"    # 负载均衡
    RESOURCE_AWARE = "resource_aware" # 资源感知
    DEADLINE = "deadline"            # 截止时间调度

class TaskType(Enum):
    """任务类型枚举"""
    MODEL_LOAD = "model_load"        # 模型加载
    MODEL_INFERENCE = "model_inference" # 模型推理
    MODEL_TRAINING = "model_training"   # 模型训练
    RESOURCE_ALLOCATION = "resource_allocation" # 资源分配
    MAINTENANCE = "maintenance"      # 维护任务

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"              # 等待中
    SCHEDULED = "scheduled"          # 已调度
    RUNNING = "running"              # 运行中
    COMPLETED = "completed"          # 已完成
    FAILED = "failed"                # 失败
    CANCELLED = "cancelled"          # 已取消

@dataclass
class SchedulingTask:
    """调度任务"""
    task_id: str
    task_type: TaskType
    priority: int                    # 1-10, 10最高
    user_id: str
    resource_requirements: Dict[str, any]
    estimated_duration: Optional[int] # 预估执行时间(秒)
    deadline: Optional[datetime]     # 截止时间
    dependencies: List[str]          # 依赖的任务ID
    metadata: Dict[str, any]
    status: TaskStatus
    created_at: datetime
    scheduled_at: Optional[datetime]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    assigned_resources: Dict[str, any]
    error_message: Optional[str]

@dataclass
class ResourceQuota:
    """资源配额"""
    user_id: str
    max_memory_mb: int               # 最大显存配额
    max_compute_percent: float       # 最大计算资源配额
    max_concurrent_tasks: int        # 最大并发任务数
    max_vgpus: int                  # 最大vGPU数量
    priority_weight: float          # 优先级权重
    valid_until: Optional[datetime] # 配额有效期

@dataclass
class SchedulingMetrics:
    """调度指标"""
    total_tasks: int
    pending_tasks: int
    running_tasks: int
    completed_tasks: int
    failed_tasks: int
    average_wait_time: float        # 平均等待时间(秒)
    average_execution_time: float   # 平均执行时间(秒)
    resource_utilization: Dict[str, float] # 资源利用率
    throughput: float               # 吞吐量(任务/小时)
    queue_length: int
    timestamp: datetime

class ResourceScheduler:
    """资源调度器"""
    
    def __init__(self, policy: SchedulingPolicy = SchedulingPolicy.RESOURCE_AWARE):
        self.policy = policy
        self.task_queue: List[SchedulingTask] = []
        self.running_tasks: Dict[str, SchedulingTask] = {}
        self.completed_tasks: Dict[str, SchedulingTask] = {}
        self.user_quotas: Dict[str, ResourceQuota] = {}
        self.lock = threading.RLock()
        self.scheduling = False
        self.scheduler_thread = None
        self.metrics_history: List[SchedulingMetrics] = []
        
        self._initialize()
    
    def _initialize(self):
        """初始化调度器"""
        try:
            # 设置默认用户配额
            self._set_default_quotas()
            
            logger.info(f"资源调度器初始化成功，策略: {self.policy.value}")
        except Exception as e:
            logger.error(f"资源调度器初始化失败: {e}")
    
    def _set_default_quotas(self):
        """设置默认配额"""
        default_quota = ResourceQuota(
            user_id="default",
            max_memory_mb=8192,
            max_compute_percent=100.0,
            max_concurrent_tasks=5,
            max_vgpus=2,
            priority_weight=1.0,
            valid_until=None
        )
        self.user_quotas["default"] = default_quota
    
    def submit_task(self, task_type: TaskType, user_id: str, 
                   resource_requirements: Dict[str, any],
                   priority: int = 5, estimated_duration: Optional[int] = None,
                   deadline: Optional[datetime] = None, dependencies: List[str] = None,
                   metadata: Dict[str, any] = None) -> str:
        """提交调度任务"""
        task_id = str(uuid.uuid4())
        
        task = SchedulingTask(
            task_id=task_id,
            task_type=task_type,
            priority=priority,
            user_id=user_id,
            resource_requirements=resource_requirements,
            estimated_duration=estimated_duration,
            deadline=deadline,
            dependencies=dependencies or [],
            metadata=metadata or {},
            status=TaskStatus.PENDING,
            created_at=datetime.now(),
            scheduled_at=None,
            started_at=None,
            completed_at=None,
            assigned_resources={},
            error_message=None
        )
        
        with self.lock:
            # 检查用户配额
            if not self._check_user_quota(user_id, resource_requirements):
                task.status = TaskStatus.FAILED
                task.error_message = "超出用户配额限制"
                self.completed_tasks[task_id] = task
                logger.warning(f"任务提交失败，超出配额: {task_id}")
                return task_id
            
            # 添加到队列
            self.task_queue.append(task)
            self._sort_task_queue()
            
        logger.info(f"任务提交成功: {task_id}, 类型: {task_type.value}")
        return task_id
    
    def _check_user_quota(self, user_id: str, requirements: Dict[str, any]) -> bool:
        """检查用户配额"""
        quota = self.user_quotas.get(user_id, self.user_quotas.get("default"))
        if not quota:
            return False
        
        # 检查当前用户的资源使用情况
        current_usage = self._get_user_resource_usage(user_id)
        
        # 检查内存配额
        required_memory = requirements.get('memory_mb', 0)
        if current_usage['memory_mb'] + required_memory > quota.max_memory_mb:
            return False
        
        # 检查计算资源配额
        required_compute = requirements.get('compute_percent', 0)
        if current_usage['compute_percent'] + required_compute > quota.max_compute_percent:
            return False
        
        # 检查并发任务数
        user_running_tasks = len([t for t in self.running_tasks.values() if t.user_id == user_id])
        if user_running_tasks >= quota.max_concurrent_tasks:
            return False
        
        return True
    
    def _get_user_resource_usage(self, user_id: str) -> Dict[str, float]:
        """获取用户当前资源使用情况"""
        usage = {
            'memory_mb': 0,
            'compute_percent': 0.0,
            'vgpu_count': 0,
            'running_tasks': 0
        }
        
        # 统计运行中任务的资源使用
        for task in self.running_tasks.values():
            if task.user_id == user_id:
                usage['running_tasks'] += 1
                if 'vgpu_id' in task.assigned_resources:
                    usage['vgpu_count'] += 1
                    # 从vGPU管理器获取实际资源使用
                    vgpu_id = task.assigned_resources['vgpu_id']
                    vgpu_info = vgpu_manager.get_vgpu_info(vgpu_id)
                    if vgpu_info:
                        usage['memory_mb'] += vgpu_info.resource.memory_mb
                        usage['compute_percent'] += vgpu_info.resource.compute_percent
        
        return usage
    
    def _sort_task_queue(self):
        """排序任务队列"""
        if self.policy == SchedulingPolicy.PRIORITY:
            self.task_queue.sort(key=lambda t: (-t.priority, t.created_at))
        elif self.policy == SchedulingPolicy.DEADLINE:
            self.task_queue.sort(key=lambda t: (t.deadline or datetime.max, -t.priority))
        elif self.policy == SchedulingPolicy.FAIR_SHARE:
            # 按用户公平性排序
            user_task_counts = {}
            for task in self.running_tasks.values():
                user_task_counts[task.user_id] = user_task_counts.get(task.user_id, 0) + 1
            
            def fair_share_key(task):
                user_running = user_task_counts.get(task.user_id, 0)
                quota = self.user_quotas.get(task.user_id, self.user_quotas.get("default"))
                weight = quota.priority_weight if quota else 1.0
                return (user_running / weight, -task.priority, task.created_at)
            
            self.task_queue.sort(key=fair_share_key)
        else:  # FIFO or RESOURCE_AWARE
            self.task_queue.sort(key=lambda t: t.created_at)
    
    def start_scheduling(self, interval: int = 5):
        """开始调度"""
        if self.scheduling:
            return
        
        self.scheduling = True
        self.scheduler_thread = threading.Thread(target=self._scheduling_loop, args=(interval,))
        self.scheduler_thread.daemon = True
        self.scheduler_thread.start()
        logger.info(f"资源调度器启动，间隔 {interval} 秒")
    
    def stop_scheduling(self):
        """停止调度"""
        self.scheduling = False
        if self.scheduler_thread:
            self.scheduler_thread.join()
        logger.info("资源调度器已停止")
    
    def _scheduling_loop(self, interval: int):
        """调度循环"""
        while self.scheduling:
            try:
                self._process_task_queue()
                self._update_running_tasks()
                self._collect_metrics()
                time.sleep(interval)
            except Exception as e:
                logger.error(f"调度循环错误: {e}")
                time.sleep(interval)
    
    def _process_task_queue(self):
        """处理任务队列"""
        with self.lock:
            if not self.task_queue:
                return
            
            # 检查依赖关系
            ready_tasks = []
            for task in self.task_queue:
                if self._check_dependencies(task):
                    ready_tasks.append(task)
            
            # 尝试调度就绪的任务
            for task in ready_tasks:
                if self._try_schedule_task(task):
                    self.task_queue.remove(task)
                    self.running_tasks[task.task_id] = task
                    task.status = TaskStatus.SCHEDULED
                    task.scheduled_at = datetime.now()
                    
                    # 启动任务执行
                    self._execute_task(task)
    
    def _check_dependencies(self, task: SchedulingTask) -> bool:
        """检查任务依赖"""
        for dep_id in task.dependencies:
            if dep_id in self.running_tasks:
                return False  # 依赖任务仍在运行
            if dep_id not in self.completed_tasks:
                return False  # 依赖任务未完成
            if self.completed_tasks[dep_id].status != TaskStatus.COMPLETED:
                return False  # 依赖任务失败
        return True
    
    def _try_schedule_task(self, task: SchedulingTask) -> bool:
        """尝试调度任务"""
        try:
            if task.task_type == TaskType.RESOURCE_ALLOCATION:
                return self._schedule_resource_allocation(task)
            elif task.task_type == TaskType.MODEL_LOAD:
                return self._schedule_model_load(task)
            elif task.task_type == TaskType.MODEL_INFERENCE:
                return self._schedule_model_inference(task)
            else:
                return self._schedule_generic_task(task)
        except Exception as e:
            logger.error(f"任务调度失败: {task.task_id}, {e}")
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            return False
    
    def _schedule_resource_allocation(self, task: SchedulingTask) -> bool:
        """调度资源分配任务"""
        requirements = task.resource_requirements
        
        # 创建vGPU分配请求
        request_id = vgpu_manager.create_allocation_request(
            requester=task.user_id,
            memory_mb=requirements.get('memory_mb', 1024),
            compute_percent=requirements.get('compute_percent', 25.0),
            bandwidth_percent=requirements.get('bandwidth_percent', 25.0),
            max_processes=requirements.get('max_processes', 1),
            priority=task.priority,
            preferred_gpu=requirements.get('preferred_gpu'),
            tags=task.metadata
        )
        
        # 尝试分配vGPU
        vgpu_id = vgpu_manager.allocate_vgpu(request_id, f"task-{task.task_id[:8]}")
        if vgpu_id:
            task.assigned_resources['vgpu_id'] = vgpu_id
            task.assigned_resources['request_id'] = request_id
            return True
        
        return False
    
    def _schedule_model_load(self, task: SchedulingTask) -> bool:
        """调度模型加载任务"""
        requirements = task.resource_requirements
        model_id = requirements.get('model_id')
        
        if not model_id:
            task.error_message = "缺少model_id参数"
            return False
        
        # 首先分配vGPU资源
        if not self._schedule_resource_allocation(task):
            return False
        
        vgpu_id = task.assigned_resources['vgpu_id']
        
        # 加载模型
        instance_id = model_manager.load_model(
            model_id=model_id,
            vgpu_id=vgpu_id,
            port=requirements.get('port')
        )
        
        if instance_id:
            task.assigned_resources['instance_id'] = instance_id
            return True
        else:
            # 如果模型加载失败，释放vGPU
            vgpu_manager.deallocate_vgpu(vgpu_id)
            return False
    
    def _schedule_model_inference(self, task: SchedulingTask) -> bool:
        """调度模型推理任务"""
        requirements = task.resource_requirements
        instance_id = requirements.get('instance_id')
        
        if not instance_id:
            task.error_message = "缺少instance_id参数"
            return False
        
        # 检查模型实例是否可用
        instance = model_manager.get_model_instance(instance_id)
        if not instance or instance.status != ModelStatus.LOADED:
            task.error_message = "模型实例不可用"
            return False
        
        task.assigned_resources['instance_id'] = instance_id
        return True
    
    def _schedule_generic_task(self, task: SchedulingTask) -> bool:
        """调度通用任务"""
        # 对于通用任务，只需要检查基本资源可用性
        return True
    
    def _execute_task(self, task: SchedulingTask):
        """执行任务"""
        def execute():
            try:
                task.status = TaskStatus.RUNNING
                task.started_at = datetime.now()
                
                # 根据任务类型执行相应操作
                if task.task_type == TaskType.RESOURCE_ALLOCATION:
                    # 资源分配任务立即完成
                    time.sleep(1)
                elif task.task_type == TaskType.MODEL_LOAD:
                    # 等待模型加载完成
                    self._wait_for_model_load(task)
                elif task.task_type == TaskType.MODEL_INFERENCE:
                    # 执行推理任务
                    self._execute_inference(task)
                else:
                    # 模拟任务执行
                    duration = task.estimated_duration or 10
                    time.sleep(duration)
                
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.now()
                
                with self.lock:
                    if task.task_id in self.running_tasks:
                        del self.running_tasks[task.task_id]
                    self.completed_tasks[task.task_id] = task
                
                logger.info(f"任务执行完成: {task.task_id}")
                
            except Exception as e:
                task.status = TaskStatus.FAILED
                task.error_message = str(e)
                task.completed_at = datetime.now()
                
                with self.lock:
                    if task.task_id in self.running_tasks:
                        del self.running_tasks[task.task_id]
                    self.completed_tasks[task.task_id] = task
                
                logger.error(f"任务执行失败: {task.task_id}, {e}")
        
        # 在后台线程中执行任务
        exec_thread = threading.Thread(target=execute)
        exec_thread.daemon = True
        exec_thread.start()
    
    def _wait_for_model_load(self, task: SchedulingTask):
        """等待模型加载完成"""
        instance_id = task.assigned_resources.get('instance_id')
        if not instance_id:
            raise Exception("缺少模型实例ID")
        
        # 等待模型加载完成，最多等待5分钟
        timeout = 300
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            instance = model_manager.get_model_instance(instance_id)
            if instance:
                if instance.status == ModelStatus.LOADED:
                    return
                elif instance.status == ModelStatus.ERROR:
                    raise Exception("模型加载失败")
            
            time.sleep(2)
        
        raise Exception("模型加载超时")
    
    def _execute_inference(self, task: SchedulingTask):
        """执行推理任务"""
        # 这里应该实现具体的推理逻辑
        # 示例：调用模型API进行推理
        time.sleep(task.estimated_duration or 5)
    
    def _update_running_tasks(self):
        """更新运行中任务状态"""
        completed_tasks = []
        
        for task_id, task in self.running_tasks.items():
            # 检查任务是否超时
            if task.deadline and datetime.now() > task.deadline:
                task.status = TaskStatus.FAILED
                task.error_message = "任务执行超时"
                task.completed_at = datetime.now()
                completed_tasks.append(task_id)
        
        # 移动完成的任务
        with self.lock:
            for task_id in completed_tasks:
                if task_id in self.running_tasks:
                    task = self.running_tasks[task_id]
                    del self.running_tasks[task_id]
                    self.completed_tasks[task_id] = task
    
    def _collect_metrics(self):
        """收集调度指标"""
        try:
            current_time = datetime.now()
            
            # 计算等待时间和执行时间
            wait_times = []
            exec_times = []
            
            for task in self.completed_tasks.values():
                if task.scheduled_at:
                    wait_time = (task.scheduled_at - task.created_at).total_seconds()
                    wait_times.append(wait_time)
                
                if task.started_at and task.completed_at:
                    exec_time = (task.completed_at - task.started_at).total_seconds()
                    exec_times.append(exec_time)
            
            # 计算资源利用率
            gpu_usage = vgpu_manager.get_gpu_usage_summary()
            resource_util = {}
            for gpu_id, usage in gpu_usage.items():
                resource_util[f"gpu_{gpu_id}_memory"] = usage['memory_utilization']
                resource_util[f"gpu_{gpu_id}_compute"] = usage['compute_allocated']
            
            # 计算吞吐量（最近1小时完成的任务数）
            hour_ago = current_time - timedelta(hours=1)
            recent_completed = len([t for t in self.completed_tasks.values() 
                                  if t.completed_at and t.completed_at > hour_ago])
            
            metrics = SchedulingMetrics(
                total_tasks=len(self.task_queue) + len(self.running_tasks) + len(self.completed_tasks),
                pending_tasks=len(self.task_queue),
                running_tasks=len(self.running_tasks),
                completed_tasks=len([t for t in self.completed_tasks.values() if t.status == TaskStatus.COMPLETED]),
                failed_tasks=len([t for t in self.completed_tasks.values() if t.status == TaskStatus.FAILED]),
                average_wait_time=sum(wait_times) / len(wait_times) if wait_times else 0,
                average_execution_time=sum(exec_times) / len(exec_times) if exec_times else 0,
                resource_utilization=resource_util,
                throughput=recent_completed,
                queue_length=len(self.task_queue),
                timestamp=current_time
            )
            
            self.metrics_history.append(metrics)
            
            # 保留最近24小时的指标
            day_ago = current_time - timedelta(hours=24)
            self.metrics_history = [m for m in self.metrics_history if m.timestamp > day_ago]
            
        except Exception as e:
            logger.error(f"收集调度指标失败: {e}")
    
    def get_task_status(self, task_id: str) -> Optional[SchedulingTask]:
        """获取任务状态"""
        # 在所有队列中查找任务
        for task in self.task_queue:
            if task.task_id == task_id:
                return task
        
        if task_id in self.running_tasks:
            return self.running_tasks[task_id]
        
        if task_id in self.completed_tasks:
            return self.completed_tasks[task_id]
        
        return None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.lock:
            # 从队列中移除
            for i, task in enumerate(self.task_queue):
                if task.task_id == task_id:
                    task.status = TaskStatus.CANCELLED
                    task.completed_at = datetime.now()
                    self.task_queue.pop(i)
                    self.completed_tasks[task_id] = task
                    logger.info(f"任务已取消: {task_id}")
                    return True
            
            # 取消运行中的任务
            if task_id in self.running_tasks:
                task = self.running_tasks[task_id]
                task.status = TaskStatus.CANCELLED
                task.completed_at = datetime.now()
                
                # 清理分配的资源
                self._cleanup_task_resources(task)
                
                del self.running_tasks[task_id]
                self.completed_tasks[task_id] = task
                logger.info(f"运行中任务已取消: {task_id}")
                return True
        
        return False
    
    def _cleanup_task_resources(self, task: SchedulingTask):
        """清理任务资源"""
        try:
            # 释放vGPU资源
            if 'vgpu_id' in task.assigned_resources:
                vgpu_id = task.assigned_resources['vgpu_id']
                vgpu_manager.deallocate_vgpu(vgpu_id)
            
            # 卸载模型
            if 'instance_id' in task.assigned_resources:
                instance_id = task.assigned_resources['instance_id']
                model_manager.unload_model(instance_id)
                
        except Exception as e:
            logger.error(f"清理任务资源失败: {task.task_id}, {e}")
    
    def set_user_quota(self, user_id: str, quota: ResourceQuota):
        """设置用户配额"""
        with self.lock:
            self.user_quotas[user_id] = quota
        logger.info(f"用户配额已更新: {user_id}")
    
    def get_metrics(self) -> Optional[SchedulingMetrics]:
        """获取最新调度指标"""
        return self.metrics_history[-1] if self.metrics_history else None
    
    def get_queue_status(self) -> Dict:
        """获取队列状态"""
        return {
            'pending_tasks': len(self.task_queue),
            'running_tasks': len(self.running_tasks),
            'completed_tasks': len(self.completed_tasks),
            'queue_details': [asdict(task) for task in self.task_queue[:10]],  # 前10个任务
            'running_details': [asdict(task) for task in self.running_tasks.values()],
            'timestamp': datetime.now().isoformat()
        }
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'policy': self.policy.value,
            'queue_status': self.get_queue_status(),
            'metrics': asdict(self.get_metrics()) if self.get_metrics() else None,
            'user_quotas': {k: asdict(v) for k, v in self.user_quotas.items()},
            'timestamp': datetime.now().isoformat()
        }

# 全局资源调度器实例
resource_scheduler = ResourceScheduler()

if __name__ == "__main__":
    # 测试代码
    scheduler = ResourceScheduler()
    
    print("=== 启动调度器 ===")
    scheduler.start_scheduling()
    
    print("\n=== 提交测试任务 ===")
    task_id = scheduler.submit_task(
        task_type=TaskType.RESOURCE_ALLOCATION,
        user_id="test_user",
        resource_requirements={
            'memory_mb': 2048,
            'compute_percent': 30.0
        },
        priority=7
    )
    print(f"任务ID: {task_id}")
    
    # 等待一段时间查看结果
    time.sleep(10)
    
    print("\n=== 队列状态 ===")
    status = scheduler.get_queue_status()
    print(f"等待任务: {status['pending_tasks']}")
    print(f"运行任务: {status['running_tasks']}")
    print(f"完成任务: {status['completed_tasks']}")
    
    scheduler.stop_scheduling()
