{"models": [{"model_id": "6fef7e09dd83bda7bbf14db78c11e65a", "name": "ollama-qwen3", "model_type": "ModelType.LLM", "framework": "ModelFramework.OLLAMA", "version": "latest", "model_path": "qwen3:latest", "config_path": null, "tokenizer_path": null, "requirements": [], "memory_requirement": 6144, "compute_requirement": 60.0, "max_batch_size": 1, "max_sequence_length": 2048, "precision": "fp16", "quantization": null, "custom_params": {}, "tags": {"source": "ollama", "model_name": "qwen3:latest"}, "description": "Qwen3 大语言模型 - 通过Ollama运行", "created_at": "2025-06-24 17:21:54.956046", "updated_at": "2025-06-24 17:21:54.956057", "api_enabled": true, "api_key": null, "base_url": null, "pricing": {"input_tokens": 0.001, "output_tokens": 0.002}}], "updated_at": "2025-06-24T19:04:30.000000"}